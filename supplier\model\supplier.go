// 自动生成模板Supplier
package model

import (
	"user/model"
	"yz-go/source"
)

type SupplierMigration struct {
	source.Model
	Name              string `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	Uid               uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;index;"`
	Realname          string `json:"realname" form:"realname" gorm:"column:realname;comment:姓名;type:varchar(255);size:255;"`
	Mobile            string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	CategoryId        uint   `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;"`
	GroupId           uint   `json:"group_id" form:"group_id" gorm:"column:group_id;comment:分组id;"`
	GoodsCount        uint   `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:上架商品数量;"`
	AllGoodsCount     uint   `json:"all_goods_count" form:"all_goods_count" gorm:"column:all_goods_count;default:0;comment:所有商品数量;"`
	HotSale           int    `json:"hot_sale" form:"hot_sale" gorm:"column:hot_sale;comment:热销数量;"`
	OrderPriceTotal   uint   `json:"order_price_total" form:"order_price_total" gorm:"column:order_price_total;comment:订单总额;type:int(20);"`
	Satisfaction      int    `json:"satisfaction" form:"satisfaction" gorm:"column:satisfaction;comment:满意度;"`
	DescribeScore     int    `json:"describe_score" form:"describe_score" gorm:"column:describe_score;comment:描述相符;"`
	ServiceScore      int    `json:"service_score" form:"service_score" gorm:"column:service_score;comment:卖家服务;"`
	ShoppingScore     int    `json:"shopping_score" form:"shopping_score" gorm:"column:shopping_score;comment:物流服务;"`
	ShopName          string `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名称;type:varchar(255);size:255;"`
	CompanyName       string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:公司名称;type:varchar(255);size:255;"`
	Province          string `json:"province" form:"province" gorm:"column:province;comment:省;type:varchar(30);size:30;"`
	City              string `json:"city" form:"city" gorm:"column:city;comment:市;type:varchar(30);size:30;"`
	County            string `json:"county" form:"county" gorm:"column:county;comment:区;type:varchar(30);size:30;"`
	ProvinceId        uint   `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省;"`
	CityId            uint   `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市;"`
	CountyId          uint   `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区;"`
	Address           string `json:"address" form:"address" gorm:"column:address;comment:详细地址;type:varchar(255);size:255;"`
	Email             string `json:"email" form:"email" gorm:"column:email;comment:联系邮箱;type:varchar(255);size:255;"`
	ShopLogo          string `json:"shop_logo" form:"shop_logo" gorm:"column:shop_logo;comment:店铺logo;type:varchar(250);size:250;"`
	Status            int    `json:"status" form:"status" gorm:"column:status;comment:状态;type:int;default:0;size:11;comment:0正常1冻结"`
	DeductionRatio    int    `json:"deduction_ratio" gorm:"default:0"`
	DeductionType     int    `json:"deduction_type" gorm:"default:2"`
	SelltType         int    `json:"sellt_type" gorm:"default:0"`
	NeedVerify        int    `json:"need_verify" gorm:"column:need_verify;default:1"`
	UserId            uint   `gorm:"<-:create;index;"`                  // 允许读和创建
	IsSelfSupport     int    `json:"is_self_support" gorm:"default:0;"` //是否自营
	BrandAuth         int    `json:"brand_auth" gorm:"default:0;"`      //1允许自创品牌，0不允许自创品牌
	SettlementBalance uint   `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;default:0;comment:结算总额;"`
	IsStorage         int    `json:"is_storage"`                                                                              //选品列表是否显示 0允许 1不允许
	IsLease           int    `json:"is_lease" gorm:"column:is_lease;type:int;default:0;size:11;comment:供应商是否显示租赁菜单 1允许 0不允许"` //供应商是否显示租赁菜单 1允许 0不允许
	ExpressMobile     string `json:"express_mobile"`
	AdminName         string `json:"admin_name" gorm:"comment:商城名称（登录页面使用）"`
	AdminTitle        string `json:"admin_title" gorm:"comment:登录标题（登录页面使用）"`
	AdminContent      string `json:"admin_content" gorm:"comment:底部信息（登录页面使用）"`
	AdminLoginLogo    string `json:"admin_login_logo" gorm:"comment:页面LOGO（登录页面使用）"`
}

func (SupplierMigration) TableName() string {
	return "suppliers"
}

// 如果含有time.Time 请自行import time包
type Supplier struct {
	source.Model
	Name              string           `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	Uid               uint             `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;index;"`
	Realname          string           `json:"realname" form:"realname" gorm:"column:realname;comment:姓名;type:varchar(255);size:255;"`
	Mobile            string           `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	CategoryId        uint             `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;"`
	GroupId           uint             `json:"group_id" form:"group_id" gorm:"column:group_id;comment:分组id;"`
	GoodsCount        uint             `json:"goods_count" form:"goods_count" gorm:"column:goods_count;comment:商品数量;"`
	AllGoodsCount     uint             `json:"all_goods_count" form:"all_goods_count" gorm:"column:all_goods_count;comment:所有商品数量;"`
	HotSale           int              `json:"hot_sale" form:"hot_sale" gorm:"column:hot_sale;comment:热销数量;"`
	OrderPriceTotal   uint             `json:"order_price_total" form:"order_price_total" gorm:"column:order_price_total;comment:订单总额;type:int(20);"`
	Satisfaction      int              `json:"satisfaction" form:"satisfaction" gorm:"column:satisfaction;comment:满意度;"`
	DescribeScore     int              `json:"describe_score" form:"describe_score" gorm:"column:describe_score;comment:描述相符;"`
	ServiceScore      int              `json:"service_score" form:"service_score" gorm:"column:service_score;comment:卖家服务;"`
	ShoppingScore     int              `json:"shopping_score" form:"shopping_score" gorm:"column:shopping_score;comment:物流服务;"`
	CategoryInfo      SupplierCategory `json:"category_info" gorm:"foreignKey:CategoryId;references:ID"`
	ShopName          string           `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名称;type:varchar(255);size:255;"`
	CompanyName       string           `json:"company_name" form:"company_name" gorm:"column:company_name;comment:公司名称;type:varchar(255);size:255;"`
	Province          string           `json:"province" form:"province" gorm:"column:province;comment:省;type:varchar(30);size:30;"`
	City              string           `json:"city" form:"city" gorm:"column:city;comment:市;type:varchar(30);size:30;"`
	County            string           `json:"county" form:"county" gorm:"column:county;comment:区;type:varchar(30);size:30;"`
	ProvinceId        uint             `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省;"`
	CityId            uint             `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市;"`
	CountyId          uint             `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区;"`
	Address           string           `json:"address" form:"address" gorm:"column:address;comment:详细地址;type:varchar(255);size:255;"`
	Email             string           `json:"email" form:"email" gorm:"column:email;comment:联系邮箱;type:varchar(255);size:255;"`
	ShopLogo          string           `json:"shop_logo" form:"shop_logo" gorm:"column:shop_logo;comment:店铺logo;type:varchar(250);size:250;"`
	Status            int              `json:"status" form:"status" gorm:"column:status;comment:状态;type:int;default:0;size:11;comment:0正常1冻结"`
	DeductionRatio    int              `json:"deduction_ratio" gorm:"default:0"`
	DeductionType     int              `json:"deduction_type" gorm:"default:2"`
	SelltType         int              `json:"sellt_type" gorm:"default:0"`
	NeedVerify        int              `json:"need_verify" gorm:"column:need_verify;default:1"`
	UserId            uint             `gorm:"<-:create;index;"` // 允许读和创建
	UserInfo          SysUser          `json:"user_info" gorm:"-"`
	IsSelfSupport     int              `json:"is_self_support"` //是否自营
	BrandAuth         int              `json:"brand_auth"`      //1允许自创品牌
	SettlementBalance uint             `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算总额;"`
	IsStorage         int              `json:"is_storage"`                                                                              //选品列表是否显示 0允许 1不允许
	IsLease           int              `json:"is_lease" gorm:"column:is_lease;type:int;default:0;size:11;comment:供应商是否显示租赁菜单 1允许 0不允许"` //供应商是否显示租赁菜单 1允许 0不允许
	ExpressMobile     string           `json:"express_mobile"`
}

// 总后台使用
type SaveSupplier struct {
	source.Model
	Name              string           `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	Realname          string           `json:"realname" form:"realname" gorm:"column:realname;comment:姓名;type:varchar(255);size:255;"`
	Mobile            string           `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	ShopName          string           `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名;type:varchar(255);size:255;"`
	GoodsCount        int              `json:"goods_count" form:"goods_count" gorm:"column:goods_count;comment:商品数量;type:int;size:11;"`
	AllGoodsCount     uint             `json:"all_goods_count" form:"all_goods_count" gorm:"column:all_goods_count;comment:所有商品数量;"`
	OrderPriceTotal   uint             `json:"order_price_total" form:"order_price_total" gorm:"column:order_price_total;comment:订单总额;type:int(20);"`
	Status            int              `json:"status"`
	DeductionRatio    *int             `json:"deduction_ratio"`
	DeductionType     int              `json:"deduction_type"`
	SelltType         *int             `json:"sellt_type" gorm:"default:0"`
	NeedVerify        int              `json:"need_verify"`
	IsStorage         int              `json:"is_storage"`
	SettlementBalance uint             `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算总额;"`
	IsLease           int              `json:"is_lease" gorm:"column:is_lease;type:int;default:0;size:11;comment:供应商是否显示租赁菜单 1允许 0不允许"` //供应商是否显示租赁菜单 1允许 0不允许
	IsSelfSupport     int              `json:"is_self_support"`                                                                         //是否自营
	ExpressMobile     *string          `json:"express_mobile"`
	AdminName         string           `json:"admin_name" gorm:"comment:商城名称（登录页面使用）"`
	AdminTitle        string           `json:"admin_title" gorm:"comment:登录标题（登录页面使用）"`
	AdminContent      string           `json:"admin_content" gorm:"comment:底部信息（登录页面使用）"`
	AdminLoginLogo    string           `json:"admin_login_logo" gorm:"comment:页面LOGO（登录页面使用）"`
	UserId            uint             `json:"user_id" gorm:"<-:create"` // 允许读和创建
	UserInfo          SysUser          `json:"user_info" gorm:"foreignKey:UserId"`
	Uid               uint             `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:int;size:11;"`
	User              model.User       `json:"user" gorm:"foreignKey:Uid"`
	CategoryId        int              `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;type:int;size:11;"`
	CategoryInfo      SupplierCategory `json:"category_info" gorm:"foreignKey:CategoryId"`
	BrandAuth         int              `json:"brand_auth"`
}

type UpdateSupplier struct {
	source.Model
	Name            string `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	Uid             uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:int;size:11;"`
	Realname        string `json:"realname" form:"realname" gorm:"column:realname;comment:姓名;type:varchar(255);size:255;"`
	Mobile          string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	CategoryId      int    `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;type:int;size:11;"`
	GoodsCount      int    `json:"goods_count" form:"goods_count" gorm:"column:goods_count;comment:商品数量;type:int;size:11;"`
	OrderPriceTotal uint   `json:"order_price_total" form:"order_price_total" gorm:"column:order_price_total;comment:订单总额;type:float;size:11;"`
	Status          int    `json:"status"`
	DeductionRatio  int    `json:"deduction_ratio"`
	DeductionType   int    `json:"deduction_type"`
	ExpressMobile   string `json:"express_mobile"`
}

// 独立后台使用
type SaveAdminSupplier struct {
	source.Model
	ShopName      string `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名称;type:varchar(255);size:255;"`
	CompanyName   string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:公司名称;type:varchar(255);size:255;"`
	Mobile        string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Province      string `json:"province" form:"province" gorm:"column:province;comment:省;type:varchar(30);size:30;"`
	City          string `json:"city" form:"city" gorm:"column:city;comment:市;type:varchar(30);size:30;"`
	County        string `json:"county" form:"county" gorm:"column:county;comment:区;type:varchar(30);size:30;"`
	ProvinceId    uint   `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省;type:varchar(30);size:30;"`
	CityId        uint   `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市;type:varchar(30);size:30;"`
	CountyId      uint   `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区;type:varchar(30);size:30;"`
	Address       string `json:"address" form:"address" gorm:"column:address;comment:详细地址;type:varchar(255);size:255;"`
	Email         string `json:"email" form:"email" gorm:"column:email;comment:联系邮箱;type:varchar(255);size:255;"`
	ShopLogo      string `json:"shop_logo" form:"shop_logo" gorm:"column:shop_logo;comment:店铺logo;type:varchar(200);size:200;"`
	ExpressMobile string `json:"express_mobile"`
}

type UpdateSupplierBasic struct {
	ID            uint   `json:"id" form:"id" gorm:"primarykey"`
	ShopName      string `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名称;type:varchar(255);size:255;"`
	ShopLogo      string `json:"shop_logo" form:"shop_logo" gorm:"column:shop_logo;comment:店铺logo;type:varchar(200);size:200;"`
	ProvinceId    uint   `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省;type:varchar(30);size:30;"`
	CityId        uint   `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市;type:varchar(30);size:30;"`
	CountyId      uint   `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区;type:varchar(30);size:30;"`
	Mobile        string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Email         string `json:"email" form:"email" gorm:"column:email;comment:联系邮箱;type:varchar(255);size:255;"`
	Address       string `json:"address" form:"address" gorm:"column:address;comment:详细地址;type:varchar(255);size:255;"`
	CompanyName   string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:公司名称;type:varchar(255);size:255;"`
	ExpressMobile string `json:"express_mobile"`
}

// 定时统计使用
type SaveTimeSupplier struct {
	source.Model
	HotSale       int    `json:"hot_sale" form:"hot_sale" gorm:"column:goods_count;comment:热销数量;type:int;size:11;"`
	Satisfaction  string `json:"satisfaction" form:"satisfaction" gorm:"column:satisfaction;comment:满意度;type:varchar(50);size:50;"`
	DescribeScore string `json:"describe_score" form:"describe_score" gorm:"column:describe_score;comment:描述相符;type:varchar(50);size:50;"`
	ServiceScore  string `json:"service_score" form:"service_score" gorm:"column:service_score;comment:卖家服务;type:varchar(50);size:50;"`
	ShoppingScore string `json:"shopping_score" form:"shopping_score" gorm:"column:shopping_score;comment:物流服务;type:varchar(50);size:50;"`
}

func (SaveSupplier) TableName() string {
	return "suppliers"
}

func (UpdateSupplier) TableName() string {
	return "suppliers"
}

func (SaveAdminSupplier) TableName() string {
	return "suppliers"
}

func (SaveTimeSupplier) TableName() string {
	return "suppliers"
}
