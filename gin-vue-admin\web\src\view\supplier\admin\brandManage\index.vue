<template>
  <m-card>
    <el-button type="primary" @click="openBrandDialog" v-if="brand_auth === 1">新增</el-button>
    <p v-else class="color-red">当前没有创建品牌权限</p>
    <el-form v-model="searchInfo" class="search-term mt25" inline>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.id" class="line-input" clearable>
            <span slot="prepend">品牌ID</span>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.name" class="line-input" clearable>
            <span slot="prepend">品牌名称</span>
        </el-input>
      </el-form-item>
      <br>
      <el-form-item>      
        <el-button type="primary" @click="getTableData(1)">搜索</el-button>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item>

      <!-- <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="品牌ID:">
            <m-num-input v-model="searchInfo.id"></m-num-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称:">
            <el-input v-model="searchInfo.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="getTableData(1)">搜索</el-button>
          <el-button type="text" @click="reSearch">重置搜索条件</el-button>
        </el-col>
      </el-row> -->
    </el-form>

    <el-table class="mt25" :data="tableData">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="图片" align="center">
        <template slot-scope="scope">
          <m-image style="width: 60px;height:60px" :src="scope.row.logo"></m-image>
        </template>
      </el-table-column>
      <el-table-column label="品牌名称" align="center" prop="name"></el-table-column>
      <el-table-column label="仓库编号" align="center" prop="consumer_data"></el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <span class="copy" @click="edit(scope.row.id)">编辑</span>
          <span class="color-red" @click="del(scope.row.id)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100,200]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes,prev, pager, next, jumper"></el-pagination>
    <brand-dialog ref="brandDialog" @reload="getTableData"></brand-dialog>
  </m-card>
</template>

<script>

import infoList from "@/mixins/infoList";
import {getBrandList, deleteBrand} from "@/api/adminSupplierBrand";
import BrandDialog from "@/view/supplier/admin/brandManage/components/brandDialog";

export default {
  name: "adminSupplierBrandIndex",
  components: {BrandDialog},
  mixins: [infoList],
  data() {
    return {
      listApi: getBrandList
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    openBrandDialog() {
      this.$refs.brandDialog.isShow = true
    },
    edit(id) {
      this.$refs.brandDialog.init(id)
    },
    async del(id) {
      const {code, msg} = await deleteBrand({id})
      if (code === 0) {
        this.$message.success(msg)
        this.getTableData()
      }
    },
    reSearch(){
      this.searchInfo = {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.color-red {
  color: red;
  margin-left: 10px;
  cursor: pointer;
  font-size: 12px;
}

.copy {
  margin-left: 10px;
  font-size: 12px;
  color: #155bd4;
  cursor: pointer;
}
</style>