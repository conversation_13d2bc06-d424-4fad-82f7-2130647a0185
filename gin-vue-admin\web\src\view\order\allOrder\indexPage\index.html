<m-card class="search-box">
    <el-form :model="formData" class="search-term" label-width="80px" inline>
        <el-form-item>
            <div class="line-input-date line-input">
                <div class="line-box">
                    <el-select class="w100" v-model="dateType">
                        <el-option
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                            v-for="item in dateTypeOptios"
                        ></el-option>
                    </el-select>
                </div>
                <div class="f fac">
                    <el-date-picker
                        class="w100"
                        placeholder="开始日期"
                        type="datetime"
                        v-model="formData.d1"
                    >
                    </el-date-picker>
                    <p class="title-3">至</p>
                    <el-date-picker
                        class="w100"
                        placeholder="结束日期"
                        type="datetime"
                        v-model="formData.d2"
                    >
                    </el-date-picker>
                </div>
            </div>
        </el-form-item>
        <el-form-item label-width="0px">
            <div class="f fac dateBtnBox">
                <span
                    :class="dateActive === item.value?'is_active':''"
                    :key="item.id"
                    @click="handleDateTab(item)"
                    v-for="item in dateList"
                    >{{ item.name }}</span
                >
            </div>
        </el-form-item>
        <br />
        <el-form-item>
            <el-input
                placeholder="请输入"
                v-model="orderSearchCondition"
                class="line-input-width"
                clearable
            >
                <el-select v-model="orderSearchConditionTag" slot="prepend">
                    <el-option
                        :label="item.name"
                        :value="item.value"
                        v-for="item in orderSearchConditions"
                    >
                    </el-option>
                </el-select>
            </el-input>
        </el-form-item>

        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>供应商</span>
                </div>
                <el-select
                    class="w100"
                    clearable
                    filterable
                    remote
                    v-model="orderSupplierConditionTag"
                    :remote-method="handleRemote"
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="平台自营" value="0"></el-option>
                    <el-option label="全部供应商" value="999999"></el-option>
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="item in orderSupplierConditions"
                    >
                    </el-option>
                    <div class="text-center">
                        <el-pagination
                            background
                            small
                            class="pagination"
                            style="
                                padding-top: 10px !important;
                                padding-bottom: 0 !important;
                            "
                            :current-page="supplierPage"
                            :page-size="supplierPageSize"
                            :total="supplierTotal"
                            @current-change="handleSupplierPage"
                            layout="prev,pager, next"
                        />
                    </div>
                </el-select>
            </div>
        </el-form-item>

        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>供应链</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="gather_supplier_id"
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="平台自营" value="0"></el-option>
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="item in supplyOptions"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <el-input
                placeholder="请输入"
                v-model="orderGoodsNameCondition"
                class="line-input"
                clearable
            >
                <span slot="prepend">商品名称</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>支付方式</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="orderPaymentTypeConditionTag"
                >
                    <el-option
                        :key="item.id"
                        :label="item.name"
                        :value="item.code"
                        v-for="item in orderPaymentTypeConditions"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>订单状态</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="orderStatusConditionsTag"
                >
                    <el-option
                        :key="item.id"
                        :label="item.name"
                        :value="item.value"
                        v-for="item in orderStatusConditions"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>采购端</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="orderApplicationConditionsTag"
                >
                    <el-option
                        v-for="item in orderApplicationConditions"
                        :key="item.id"
                        :label="item.app_name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>售后状态</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="afterSaleStatus"
                >
                    <el-option :value="-1" label="售后关闭"></el-option>
                    <el-option :value="2" label="售后中"></el-option>
                    <el-option :value="3" label="售后完成"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>直播间</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="share_live_id"
                >
                    <el-option
                        v-for="item in shareLiveOption"
                        :key="item.id"
                        :value="item.id"
                        :label="item.title"
                    ></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" v-if="dianpushow === 1">
            <el-input
                placeholder="请输入"
                v-model="source_shop_name"
                class="line-input"
                clearable
            >
                <span slot="prepend">供应链店铺名称</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <el-input
                placeholder="请输入"
                v-model="app_shop_name"
                class="line-input"
                clearable
            >
                <span slot="prepend">商城名称</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <el-input
                placeholder="请输入"
                v-model="note"
                class="line-input"
                clearable
            >
                <span slot="prepend">订单备注</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>订单类型</span>
                </div>
                <el-select
                    class="w100"
                    filterable
                    clearable
                    v-model="orderTypeConditionsTag"
                    @change="orderTypeConditionsOnChange"
                >
                    <el-option
                        v-for="item in orderTypeConditions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.name"
                    ></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <el-input
                placeholder="请输入"
                v-model="order_type_note"
                class="line-input"
                clearable
            >
                <span slot="prepend">{{ order_type_note_title }}</span>
            </el-input>
        </el-form-item>
        <el-form-item label="异常订单:">
            <el-radio-group v-model="gather_supplier_status">
                <el-radio
                    :key="item.id"
                    :label="item.value"
                    v-for="item in gather_supplier_status_options"
                >
                    {{ item.label }}
                </el-radio>
            </el-radio-group>
        </el-form-item>
        <!-- <el-col :xl="8" :lg="12">
                <el-form-item label-width="0px">
                    <div class="f fac dateBtnBox">
                        <span @click="handleDateTab(item)" v-for="item in dateList"
                            :class="dateActive === item.value?'is_active':''" :key="item.id">{{item.name}}</span>
                    </div>
                </el-form-item>
            </el-col> -->
        <br />
        <el-form-item>
            <el-button @click="searchOrder()" type="primary">搜索</el-button>
            <el-button @click="orderExport">导出</el-button>
            <el-button @click="clearSearchCondition()" type="text"
                >重置搜索条件</el-button
            >
            <el-button @click="orderReportForms()" type="text"
                >订单报表</el-button
            >
        </el-form-item>
    </el-form>
    <el-tabs
        @tab-click="handleTabsClick"
        class="mt25 order-tabs"
        type="card"
        v-model="orderStatus"
    >
        <el-tab-pane
            :key="item.id"
            :label="`${item.name} ${item.total !== null?item.total:''}`"
            :name="item.value"
            v-for="item in orderStatusConditions"
        >
        </el-tab-pane>
    </el-tabs>
    <el-button
        class="mt10"
        type="text"
        @click="allAdviseReceiptOfGoods()"
        v-if="orderStatus === '2'"
    >
        全部通知发货
    </el-button>
    <div class="table-box">
        <el-table
            :data="[{}]"
            class="table-head"
            :header-cell-style="{ height: '56px'}"
        >
            <el-table-column label="商品" width="300"></el-table-column>
            <el-table-column
                align="center"
                label="单件(元)/数量"
                width="200"
            ></el-table-column>
            <el-table-column
                align="center"
                label="手机号(ID)/会员昵称"
                width="200"
            ></el-table-column>
            <el-table-column
                align="center"
                label="付款方式/配送方式"
                width="200"
            ></el-table-column>
            <el-table-column
                align="center"
                label="小计/运费/应付款"
                width="250"
            ></el-table-column>
            <el-table-column
                align="center"
                label="订单状态"
                width="200"
            ></el-table-column>
            <el-table-column align="center" label="操作"></el-table-column>
        </el-table>
        <div :key="item.id" v-for="item in orderList">
            <el-table
                :data="item.order_items"
                :span-method="objectSpanMethod"
                class="table-cont"
            >
                <el-table-column>
                    <template slot="header">
                        <div class="w100 f fac fjsb">
                            <div class="f fac fw">
                                <p>订单ID: {{ item.id }}</p>
                                <p>订单编号: {{ item.order_sn }}</p>
                                <p v-if="item.third_order_sn">
                                    第三方订单编号: {{ item.third_order_sn }}
                                </p>
                                <p v-if="item.pay_info.pay_sn">
                                    支付单号: {{ item.pay_info.pay_sn }}
                                </p>
                                <p v-if="item.gather_supply_id > 0">
                                    供应链单号:
                                    <span v-if="item.gather_supply_sn">
                                        {{ item.gather_supply_sn }}
                                        <span
                                            class="color-red"
                                            v-if="!item.gather_supply_sn && item.gather_supply.category_id === 2 "
                                        >
                                            {{ item.gather_supply_msg }}
                                        </span>
                                        <span
                                            class="color-red"
                                            v-if="item.gather_supply.category_id === 6"
                                        >
                                            {{ item.gather_supply_msg }}
                                        </span>
                                    </span>
                                    <span
                                        class="color-red"
                                        v-else-if="!item.gather_supply_sn && item.gather_supply.category_id > 0"
                                    >
                                        订单异常<span
                                            v-if="item.gather_supply_msg"
                                        >
                                            {{ item.gather_supply_msg }}</span
                                        >
                                    </span>
                                    <!--                                    <span class="color-red"
                                          v-else-if="item.gather_supply_type === 7 && !item.gather_supply_sn">
                                        <el-tooltip effect="dark" :content="item.gather_supply_msg" placement="top">
                                        <span><i class="el-icon-warning"></i> 订单异常</span>
                                    </el-tooltip>
                                    </span>-->
                                    <!--                                    <span class="color-red"
                                          v-else-if="!item.gather_supply_sn && (item.gather_supply.category_id === 4 || item.gather_supply.category_id === 1 || item.gather_supply.category_id === 2 || item.gather_supply.category_id === 6 || item.gather_supply.category_id === 7)">
                                        订单异常<span v-if="item.gather_supply_msg"> {{ item.gather_supply_msg }}</span>
                                    </span>-->
                                </p>
                                <p v-if="item.cloud_order.cloud_order_id">
                                    云仓订单id: {{
                                    item.cloud_order.cloud_order_id }}
                                </p>
                                <p
                                    v-if="orderStatus === '2' || orderStatus === '3'"
                                >
                                    发货时间: {{ item.sent_at | formatDate }}
                                </p>
                                <p v-else>
                                    下单时间: {{ item.created_at | formatDate }}
                                </p>
                                <p v-if="item.paid_at">
                                    支付时间：{{ item.paid_at | formatDate }}
                                </p>
                                <el-tag
                                    class="mr10"
                                    type="warning"
                                    v-if="item.cloud_order.id !==0"
                                    >云仓订单</el-tag
                                >
                                <el-tag
                                    class="mr10"
                                    type="warning"
                                    v-if="item.order_type ===1"
                                    >套餐订单:{{item.order_type_note}}</el-tag
                                >

                                <el-tag type="warning"
                                    >{{ item.shop_name }}</el-tag
                                >
                                <el-tag
                                    type="warning"
                                    class="ml10"
                                    v-if="item.share_live_room_id"
                                    >{{item.share_live_room.title}}</el-tag
                                >
                            </div>
                        </div>
                        <div>
                            <p
                                v-if="item.gather_supply_type === 7 && !item.gather_supply_sn"
                                class="color-red"
                            >
                                {{item.gather_supply_msg}}
                            </p>
                        </div>
                    </template>
                    <el-table-column width="300">
                        <template slot-scope="scope">
                            <div class="f fac goods-box">
                                <m-image
                                    :src="scope.row.image_url"
                                    style="
                                        width: 60px;
                                        height: 60px;
                                        border-radius: 16px 16px 16px 16px;
                                    "
                                >
                                </m-image>
                                <div class="f1">
                                    <!--题目两行缩略class： <p class="hiddenText2"> -->
                                    <p
                                        style="
                                            height: auto !important;
                                            line-height: 23px !important;
                                        "
                                    >
                                        <a
                                            @click="$_blank('/layout/goodsIndex/addGoods',{id:scope.row.product_id})"
                                            href="javascript:;"
                                            style="color: #155bd4"
                                        >
                                            {{ scope.row.title }}
                                        </a>
                                    </p>
                                    <p style="color: #a7a7a7">
                                        规格: {{ scope.row.sku_title }}
                                    </p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="210">
                        <template slot-scope="scope">
                            <div class="comm-box" style="width: 85%">
                                <!-- <p v-for="amountItem in scope.row.amount_detail.amount_items">{{amountItem.title}}: {{ amountItem.amount | formatF2Y }}</p> -->
                                <p>
                                    供货单价: {{ scope.row.amount /
                                    scope.row.qty | formatF2Y }}
                                </p>
                                <p>数量: {{ scope.row.qty }}</p>
                                <p>
                                    金额小计: {{ scope.row.amount | formatF2Y }}
                                </p>
                                <p>子订单号: {{ scope.row.id }}</p>
                                <p
                                    v-if="item.gather_supply_type === 119 || item.gather_supply_type === 130 || item.gather_supply_type === 122"
                                >
                                    供应链子单号: {{ scope.row.gather_supply_sn
                                    }}
                                </p>
                                <p
                                    v-if="scope.row.refund_status === 3"
                                    class="color-red"
                                >
                                    售后完成
                                </p>
                                <p
                                    v-if="scope.row.after_sales.id"
                                    @click="openAfterDetail(scope.row)"
                                    class="color-red mt_6 shou"
                                >
                                    查看售后
                                </p>
                                <!-- <el-button @click="openAfterDetail(scope.row)"
                                           class="color-red"
                                           style="padding: 0;margin:0;" type="text" v-if="scope.row.after_sales.id">查看售后
                                </el-button> -->
                                <p
                                    v-if="item.gather_supply && item.gather_supply.category_id === 122 && item.status === 1"
                                    @click="updateOddNum('2',scope.row.id,item.gather_supply_id)"
                                    style="color: #155bd4"
                                >
                                    上传单号
                                </p>
                                <p
                                    @click="openDiscounts(scope.row.amount_detail.amount_items)"
                                >
                                    ......
                                </p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box">
                                <p
                                    class="title-3"
                                    style="
                                        height: auto !important;
                                        line-height: 23px !important;
                                    "
                                >
                                    <span
                                        @click="toUserInfo(item.user.id)"
                                        type="text"
                                        style="color: #155bd4"
                                    >
                                        {{ item.user.username }}({{ item.user.id
                                        }})
                                    </span>
                                </p>
                                <p class="title-3">
                                    {{ item.user ? item.user.nickname : '' }}
                                </p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box">
                                <p class="title-3">{{ item.pay_type }}</p>
                                <p class="title-3">快递</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="250">
                        <template slot-scope="scope">
                            <div class="comm-box" style="width: 85%">
                                <div
                                    v-for="amount_items in item.amount_detail.amount_items"
                                >
                                    <p>
                                        {{ amount_items.title }}: ￥{{
                                        amount_items.amount | formatF2Y }}
                                    </p>
                                </div>
                                <p>
                                    应付款: ￥{{ item.amount_detail.amount |
                                    formatF2Y }}
                                </p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box text-center">
                                <p class="title-3">
                                    {{ item.status | formatStatus }}
                                </p>
                                <template
                                    v-for="(ctm,index) in item.order_expresss.length === 0 ? (item.order_expresss.length) + 1:item.order_expresss.length "
                                >
                                    <span
                                        @click="openPackageLogistics(item)"
                                        type="text"
                                        v-if="item.status === -1 || item.status >= 2"
                                        style="margin-left: 0; color: #155bd4"
                                    >
                                        <span>包裹{{ index + 1 }}:</span
                                        >查看物流
                                    </span>
                                </template>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column>
                        <template slot-scope="scope">
                            <div class="title-3" style="align-items: center">
                                <template
                                    v-if="item.gather_supply && item.gather_supply.category_id === 122 && item.status === 1"
                                >
                                    <el-button
                                        @click="updateOddNum('1',item.id,item.gather_supply_id)"
                                        type="text"
                                    >
                                        上传单号
                                    </el-button>
                                </template>
                                <template v-if="item.lock === 0">
                                    <el-button
                                        @click="orderOperationDialog(item,btn)"
                                        type="text"
                                        v-for="btn in item.button"
                                        >{{ btn.title }}
                                    </el-button>
                                </template>
                                <!--                                <template v-else-if="item.lock === 1 && item.status === 1">-->
                                <template v-else-if="item.lock === 1">
                                    <el-button
                                        @click="reLock(item.id)"
                                        class="color-red"
                                        type="text"
                                        >解除锁定
                                    </el-button>
                                    <el-button
                                        v-if="item.gather_supply_sn == '' || item.status === 1"
                                        @click="submitOrder(item.id)"
                                        class="color-red"
                                        type="text"
                                        >提交订单
                                    </el-button>
                                </template>
                                <template
                                    v-if="item.da_hang_erp_order.id && item.da_hang_erp_order.status !== -2  && item.status !== -1"
                                >
                                    <el-button
                                        @click="pushDachanghangOrder(item)"
                                        class="color-red"
                                        type="text"
                                        >推送至大昌行
                                    </el-button>
                                </template>
                                <template
                                    v-if="item.pay_type_id == 10 && item.is_wx_mini_send == -1"
                                >
                                    <el-button
                                        @click="openDeliveryInformation(item)"
                                        type="text"
                                        >小程序同步发货
                                    </el-button>
                                </template>
                                <el-button
                                    type="text"
                                    v-if="item.status >= 2"
                                    @click="adviseReceiptOfGoods(item.id)"
                                >
                                    通知发货
                                </el-button>
                            </div>
                            <!--                            <div class="title-3" v-else-if="item.lock === 1 && item.status === 1 ">
                                <el-button @click="reLock(item.id)" class="color-red"
                                           type="text">解除锁定
                                </el-button>
                                <el-button @click="submitOrder(item.id)" class="color-red"
                                           type="text">提交订单
                                </el-button>
                            </div>-->
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>
            <div class="table-foot-box">
                <div class="f fac fjsb">
                    <!--                    <p> {{ assemblyAddress(item.shipping_address) }}</p>-->
                    <!--                    <p>{{ item}}</p>-->
                    <p>
                        {{ assemblyAddress(item.shipping_address) }}
                        <a
                            @click="openEditAddressDialog(item)"
                            class="color-red ml10"
                            href="javascript:;"
                            v-if="item.status <= 1 && item.is_update_shipping_address === 1"
                            >修改地址</a
                        >
                        <el-button
                            type="text"
                            class="ml10 color-red"
                            v-if="assemblyAddress(item.shipping_address)"
                            @click="$fn.copy(assemblyAddress(item.shipping_address))"
                            >复制
                        </el-button>
                        <span
                            class="ml10"
                            v-if="item.application_shop.shop_name"
                            >多商城名: {{ item.application_shop.shop_name
                            }}</span
                        >
                    </p>
                    <p v-if="item.gather_supply_type == '116'">
                        阿里店铺:{{item.source_shop_name}}
                    </p>
                    <p v-if="item.gather_supply_type == '14'">
                        聚水潭店铺:{{item.source_shop_name}}
                    </p>
                    <p v-if="item.gather_supply_type == '136'">
                        旺店通店铺:{{item.source_shop_name}}
                    </p>
                    <p v-if="item.gather_supply_type == '116'">
                        供应链订单金额:{{item.gather_supply_amount | formatF2Y}}
                    </p>
                    <el-tag
                        type="danger"
                        class="ml10"
                        v-if="item.da_hang_erp_order.status === -1"
                        >{{item.da_hang_erp_order.push_error_msg }}</el-tag
                    >
                    <div class="f fac">
                        <template v-if="aliBtnIsShow">
                            <a
                                v-if="item.supplier.id && item.status === 1 && item.gather_supply_sn === ''"
                                @click="pushAlibaba(item)"
                                class="close-order color-red"
                                href="javascript:;"
                                >推送阿里巴巴</a
                            >
                        </template>
                        <p
                            @click="dialogIsShow(item)"
                            class="color-red shou mr10"
                        >
                            <span
                                style="
                                    display: inline-block;
                                    width: 150px;
                                    overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                "
                                v-if="item.note"
                                >备注 : {{ item.note }}</span
                            >
                            <span v-else>备注</span>
                        </p>
                        <!--                        <p v-if="item.note !==''" @click="dialogIsShow(item)"
                                                   style="color: red ;margin-right: 20px;width: 150px; overflow:hidden; white-space:nowrap;text-overflow:ellipsis;cursor: pointer">
                                                    备注 : {{ item.note }}</p>-->
                        <template v-if="item.print_button">
                            <span class="cgray mr10"
                                >{{ item.print_button[0].title }}</span
                            >
                            <el-dropdown trigger="click" class="hauto">
                                <a
                                    href="javascript:;"
                                    class="primary close-order"
                                    >打印电子面单</a
                                >
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item
                                        v-for="(btn,bindex) in item.print_button"
                                    >
                                        <p
                                            v-if="bindex !== 0"
                                            @click="handlePrintClick(btn,item.id)"
                                        >
                                            {{ btn.title }}
                                        </p>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                        <a
                            @click="openDetail(item)"
                            class="primary close-order"
                            :class="item.print_button ? 'ml10' : ''"
                            href="javascript:;"
                            >查看详情</a
                        >
                    </div>
                    <!-- <p>{{item.shipping_address.mobile}}</p>
                    <p class="addr-p">
                        {{assemblyAddress(item.shipping_address)}}
                        {{item.province}} {{item.city}} {{item.county}} {{item.detail}}
                    </p> -->
                </div>
                <p v-if="item.remark" style="margin-left: 0; color: red">
                    买家留言: {{item.remark}}
                </p>
                <p
                    class="mb_5"
                    style="margin-left: 0"
                    v-if="item.gather_supply_type === 122"
                >
                    匹配三方地址: {{ item.shipping_address.third_address }}
                    {{item.shipping_address.detail}}
                </p>
            </div>
        </div>
        <el-pagination
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            background
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <order-detail ref="orderDetail"></order-detail>
        <user-detail ref="userDetail"></user-detail>
        <order-shipments-dialog
            @reload="initOrder"
            ref="orderShipmentsDialog"
        ></order-shipments-dialog>
        <edit-logistics-dialog
            ref="editLogisticsDialog"
        ></edit-logistics-dialog>
        <detail-after-sale
            @reload="initOrder"
            ref="detailAfterSale"
        ></detail-after-sale>
        <print-surface-single-dialog
            ref="PrintSurfaceSingleDialog"
            @reload="initOrder"
        ></print-surface-single-dialog>
        <multiple-packages-dialog
            ref="MultiplePackagesDialog"
            @getData="printPackages"
        ></multiple-packages-dialog>
        <address-dialog
            :addressDialogIsShow.sync="addressDialogIsShow"
            @reload="initOrder"
            ref="addressDialog"
            v-if="addressDialogIsShow"
        ></address-dialog>
        <el-dialog
            :before-close="handleDialogClose"
            :visible="isShow"
            title="备注"
            top="40vh"
            width="600px"
        >
            <el-input
                :rows="6"
                placeholder="请输入内容"
                type="textarea"
                v-model="dialogForm.note"
            >
            </el-input>
            <div class="dialog-footer" slot="footer">
                <el-button @click="confirmNote" type="primary">确 定</el-button>
                <el-button @click="handleDialogClose">取 消</el-button>
            </div>
        </el-dialog>
    </div>
    <!-- 查看物流详情 -->
    <package-logistics-dialog
        ref="packageLogisticsDialog"
    ></package-logistics-dialog>
    <!-- 发货信息推送 -->
    <delivery-information-dialog
        ref="deliveryInformationDialog"
    ></delivery-information-dialog>
    <!-- 订单报表 -->
    <order-report-forms-dialog
        ref="orderReportFormsDialog"
    ></order-report-forms-dialog>
    <editPriceDialog
        ref="editPriceDialog"
        @reload="initOrder"
    ></editPriceDialog>
    <editPriceLogDialog ref="editPriceLogDialog"></editPriceLogDialog>
</m-card>
