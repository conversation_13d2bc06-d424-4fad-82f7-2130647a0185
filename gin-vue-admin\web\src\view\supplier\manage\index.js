import {
    createSupplier,
    deleteSupplier,
    findSupplier,
    getSupplierList,
    updateSupplier,
    ChangePassword,
} from "@/api/supplier"; //  此处请自行替换地址
import {
    getSupplierCategoryList
} from "@/api/supplierCategory";
import {getUserList} from "@/api/member"
import verify from "@/utils/verify";
import itemAlign from "../../../components/gva-wfd/behavior/itemAlign";
import {mapGetters} from "vuex";

export default {
    name: "Supplier",
    data() {
        return {
            path: this.$path,
            copyVisible:false,
            passwordTable: [],
            dialogPasswordTitle: "修改密码",
            dialogTitle: "新增",
            supplierCategoryList: [],
            pageSize: 10,
            page: 1,
            total: 0,
            tableData: [],
            userOption: [],
            userOptionsPage: {
                page: 1,
                pageSize: 20,
                total: 0,
            },
            formData: {
                type: "",
                name: "",
                keyword: "",
                category_id: "",
            },
            dpid:'',
            dptype:'',
            dialogFormVisible: false,
            dialogPasswordFormVisible: false,
            supplierData: {
                sellt_type: 0,
                name: "",
                deduction_type: 2,
                need_verify: 1,
                brand_auth: 0,
                is_lease:1,
                is_storage: 0,
                category_id: "",
                uid: "",
                realname: "",
                mobile: "",
                express_mobile: '',
                user_info: {
                    username: "",
                    password: "",
                },
                admin_name: '',
                admin_content: '',
                admin_login_logo: '',
                admin_title: '',
            },
            change: {
                new_password: "",//修改密码
                password_again: "",//确认修改密码

            },
            type: "",
            rule: {
                uid: {required: true, message: "请选择会员", trigger: "change"},
                name: [{required: true, message: "请填写供应商名称", trigger: "blur"}],
                realname: [{required: true, message: "请输入姓名", trigger: "blur"}],
                user_info: {
                    username: [{required: true, message: "请输入用户名", trigger: "blur"}],
                    password: [{required: true, message: "请输入密码", trigger: "blur"}],
                },
                //mobile: [{ required: true, message: "请输入手机号", trigger: "blur" }],
                // 法人手机号
                mobile: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur",
                    },
                    {
                        validator: (rule, value, callback) => {
                            let res = verify.checkPhone(value);
                            return res ? callback() : callback(new Error("手机号格式不正确"));
                        },
                    },
                ],
                category_id: {required: true, message: "请选择分类", trigger: "change"},
            },
            rulePassword: {
                new_password: [{required: true, message: "请输入修改密码", trigger: "blur"}],
                password_again: [{required: true, message: "请输入确认修改密码", trigger: "blur"}],
            }
        };
    },
    mounted() {
        this.initSupplierCategory();
        this.initSupplierList();
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    methods: {
        // 图片大小限制
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        // 后台登录页LOGO
        handleAdminLoginLogoImgSuccess(res) {
            this.supplierData.admin_login_logo = res.data.file.url;
        },
        //复制链接
        copyLink(row) {
            console.log(row)
            this.copyVisible = true
            this.dpid = row.id
        },
        //复制链接
        pclinkCopy(){
            let link = ""
            if (location.hostname === "localhost") {
                link = location.protocol + "//localhost:9527/store?sid=" +this.dpid + "&type=" + 0
            } else {
                link = location.origin + "/store?sid="  +this.dpid + "&type=" + 0
            }
            this.$fn.copy(link)
        },
        H5linkCopy(){
            let link = ""
            if (location.hostname === "localhost") {
                link = location.protocol  + "//localhost:9527"+ "/h5/?menu#/"+ "packageB/store/storeDetails?sid=" +this.dpid + "&type=" + 0
            } else {
                link = location.origin  + "/h5/?menu#/" + "/packageB/store/storeDetails?sid=" + this.dpid + "&type=" + 0
            }
            this.$fn.copy(link)
        },
        appletlinkCopy(){
            let link = "/packageB/store/storeDetails?sid="+  this.dpid + "&type=" + 0
            this.$fn.copy(link)
        },
        // 后台链接
        adminlinkCopy() {
            let link = ""
            if (location.hostname === "localhost") {
                link = location.protocol + "//localhost:8080/#/login?id=" + this.dpid
            } else {
                link = location.origin + "/super/#/login?id=" + this.dpid
            }
            this.$fn.copy(link)
        },
        remoteMethod(query) {
            this.userOptionsPage.page = 1
            this.getUserOption(query)
        },
        handleUserPage(val) {
            this.userOptionsPage.page = val
            this.getUserOption()
        },
        //获取供应商分类
        initSupplierCategory() {
            getSupplierCategoryList().then(res => {
                if (res.code === 0) {
                    this.supplierCategoryList = res.data.list;
                }
            });
        },

        //获取列表数据
        initSupplierList() {
            let para = {
                "pageSize": this.pageSize,
                "page": this.page,
            }
            switch (this.formData.type) {
                case "1":
                    para.uid = this.formData.keyword;
                    break;
                case "2":
                    para.realname = this.formData.keyword;
                    break;
                case "3":
                    para.mobile = this.formData.keyword;
                    break;
                default:
                    break;
            }
            if (this.formData.category_id == null || this.formData.category_id == "") {
            } else {
                para.category_id = this.formData.category_id;
            }
            para.name = this.formData.name;

            getSupplierList(para).then(res => {
                if (res.code === 0) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }
            });
        },

        //删除item
        deleteSupplierDialog(row) {
            let s = row.status == 0 ? '确定冻结此供应商吗?冻结后此供应商商品将全部冻结，供应商后台无法登录。' : '确定启用此供应商吗?'
            this.$confirm(s, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                this.deleteSupplier(row);
            }).catch(() => {
            });
        },
        //cupy off
        copyCloseBrand(){
            this.copyVisible = false
        },
        deleteSupplier(row) {
            let para = {
                id: row.id
            }
            deleteSupplier(para).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.initSupplierList();
                }
            });
        },

        //条件搜索前端看此方法
        onSubmit() {
            this.page = 1;
            this.pageSize = 10;
            this.tableData = [];
            this.initSupplierList();
        },

        // 重置搜索条件
        resetSearch() {
            this.formData.name = "";
            this.formData.type = "";
            this.formData.category_id = "";
            this.formData.keyword = "";
        },

        handleSizeChange(size) {
            this.pageSize = size
            this.initSupplierList();
        },

        handleCurrentChange(page) {
            this.page = page
            this.initSupplierList();
        },

        //打开新增,编辑页
        openDialog() {
            this.dialogFormVisible = true;
        },
        //关闭修改密码
        closePasswordDialog() {
            this.dialogPasswordFormVisible = false;
            this.change = {
                new_password: "",
                password_again: "",
            }
        },
        closeDialog() {
            this.dialogFormVisible = false;
            this.supplierData = {
                deduction_type: 2,
                sellt_type: 0,
                need_verify: 1,
                brand_auth: 0,// 品牌权限2不可以添加 1可以添加
                is_storage: 0,
                is_lease:0,
                name: "",
                uid: "",
                realname: "",
                mobile: "",
                express_mobile: '',
                user_info: {
                    username: "",
                    password: "",
                },
                category_id: "",
                admin_name: '',
                admin_content: '',
                admin_login_logo: '',
                admin_title: '',
            };
            this.userOptionsPage = {
                page: 1,
                pageSize: 20,
                total: 0,
            }
            this.type = "";
            this.$refs["el-forms"].resetFields();
        },

        //新增
        newSupplier() {
            this.openDialog();
            this.dialogTitle = "新增";
            this.type = "create";
            this.getUserOption()
        },
        // 获取会员
        getUserOption(username = "") {
            let params = {}
            if (username) {
                params.username = username
            }
            getUserList({
                page: this.userOptionsPage.page,
                pageSize: this.userOptionsPage.pageSize, ...params
            }).then(res => {
                if (res.code === 0) {
                    this.userOption = res.data.list
                    this.userOptionsPage.total = res.data.total
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        //赋值表单
        setFrom(val) {
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.supplierData[element] = val[element];
            });
        },
        //编辑
        editSupplier(item) {
            this.openDialog();
            this.dialogTitle = "编辑";
            this.type = "edit";
            this.getUserOption()
            let para = {
                "id": item.id
            }
            findSupplier(para).then(res => {
                if (res.code === 0) {
                    res.data.resupplier.deduction_ratio = res.data.resupplier.deduction_ratio / 100
                    res.data.resupplier.deduction_type = res.data.resupplier.deduction_type === 0 ? 2 : res.data.resupplier.deduction_type;
                    res.data.resupplier.need_verify = res.data.resupplier.need_verify === 0 ? 1 : res.data.resupplier.need_verify;
                    this.setFrom(res.data.resupplier)
                    // this.supplierData = res.data.resupplier;
                }
            });
        },
        //修改密码
        changePassword(id) {
            this.dialogPasswordFormVisible = true
            this.dialogPasswordTitle = "修改密码";
            this.passwordTable = [],
                this.tableData.forEach(item => {
                    if (id === item.id) {
                        this.passwordTable.push(item.id)
                    }
                })
        },
        //确认修改密码
        submitPassword() {
            this.$refs["el-password"].validate((valid) => {
                if (!valid) return;
                let data = {
                    "id": parseInt(this.passwordTable),
                    "new_password": this.change.new_password,
                    "password_again": this.change.password_again,
                }
                ChangePassword(data).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.closePasswordDialog()
                        this.initSupplierList();
                    }
                })
            });

        },
        //确认数据
        submitVerify() {
            switch (this.type) {
                case "create":
                    this.submitVerifyCreate();
                    break;
                case "edit":
                    this.editVerifyCreate();
                    break;
                default:
                    break;
            }
        },

        submitVerifyCreate() {
            this.$refs["el-forms"].validate((valid) => {
                if (!valid) return;
                this.createSupplier();
            });
        },

        editVerifyCreate() {
            this.$refs["el-forms"].validate((valid) => {
                if (!valid) return;
                this.updateSupplier();
            });
        },

        //创建
        createSupplier() {
            this.supplierData.uid = parseInt(this.supplierData.uid);
            createSupplier(this.supplierData).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.closeDialog();
                    this.initSupplierList();
                }
            });
        },

        //更新
        updateSupplier() {
            if (this.supplierData.express_mobile) {
                let express_mobile = verify.checkPhone(this.supplierData.express_mobile.trim());
                if (!express_mobile) {
                    this.$message.error('寄件人手机号格式不正确')
                    return
                }
            }
            this.supplierData.deduction_ratio = this.supplierData.deduction_ratio * 100
            this.supplierData.uid = parseInt(this.supplierData.uid);
            updateSupplier(this.supplierData).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.closeDialog();
                    this.initSupplierList();
                }
            });
        }
    }
};