import SelectClassify from '../selectClassify';
/* import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { quillEditor, Quill } from 'vue-quill-editor';
import { container, ImageExtend, QuillWatch } from 'quill-image-extend-module' */
import { createProduct, findProduct, updateProduct } from '@/api/product';
import { mapGetters } from 'vuex';
import { getClassify, getClassifySelect } from '@/api/classify';
//导入draggable组件
import draggable from 'vuedraggable';
import cnchar from 'cnchar';
import { getExpressTemplateList } from '@/api/expressTemplate';
import TempDialog from '@/view/order/distributionTemp/components/templateDialog';
import SkusDescribeDialog from '../skusDescribeDialog';
// Quill.register('modules/ImageExtend', ImageExtend)
import { getBillCategory } from '@/api/bill';
import CodeTypeDialog from '../codeTypeDialog';
import { getBrandOptionList } from '@/api/brands';
import { findApplicationSetting } from '@/api/application';
import { getSupplySettin, getUnit } from '@/api/goods';
import { getBrandsList } from '@/api/brands';
import {
    getSupplierSourceCategoryOptionList,
    getSupplierSourceOptionList,
} from '@/api/supplier/source';
import {
    getCategoriesByCondition,
    smallShopVideoproductsaveDraft,
    smallShopVideoproductfindDraft,
    smallShopVideoproductgetImgs,
    smallShopVideofreightsync,
    smallShopVideobrandlist,
    smallShopVideocategorydetail,
    smallShopVideoBrandneedSync,
    smallShopVideocategorysync,
    smallShopVideofreightget,
} from '@/api/smallShopVideo';
import PinyinMatch from 'pinyin-match';
import SkusIntroduce from '../skusIntroduce.vue';
import { getUserLevelList } from '@/api/member';

/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};
export default {
    name: 'addGoods',
    // components: { SelectClassify, quillEditor, draggable, TempDialog },
    components: {
        SelectClassify,
        draggable,
        TempDialog,
        SkusDescribeDialog,
        CodeTypeDialog,
        SkusIntroduce,
    },
    data() {
        return {
            productInfo: {},
            isShow: false,
            verifySkusPrice: true,
            plugin_widget: null, // 判断视频号
            showSelect: true, // 品牌的第一条数据
            brand_name: '', // 品牌名称
            supplier_id: null,
            source_id: null,
            source_classify_id: null,
            source_options: [],
            source_classify_options: [],
            priceIsEdit: 0, // 0允许改动价格  1不允许改动价格
            original_sku_id: '0',
            singleSkuImg: '', // 单规格图片
            minBuyQty: 0, // 最少起批量
            brandsOptiosData: {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            },
            copyBrandOptios: [],
            smallbrandsOptiosData: {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            },
            /* 发票部分开始 */
            treeInput: '', // 搜索字段
            treeData: [], // 分类数据
            taxRateOptios: [
                // 税率optios
                { label: '0%', value: 0 },
                { label: '1%', value: 1 },
                { label: '3%', value: 3 },
                { label: '6%', value: 6 },
                { label: '9%', value: 9 },
                { label: '10%', value: 10 },
                { label: '11%', value: 11 },
                { label: '13%', value: 13 },
                { label: '17%', value: 17 },
            ],
            freeOfTaxOptions: [
                { label: '正常税率', value: 1 },
                { label: '出口免税和其他免税优惠政策', value: 2 },
                { label: '不征增值税', value: 3 },
                { label: '普通零税率', value: 4 },
            ],
            favorablePolicyOptios: [
                '免税',
                '100%先征后退',
                '50%先征后退不征税',
                '先征后退',
                '即征即退100%',
                '即征即退30%',
                '即征即退50%',
                '即征即退70%',
                '按3%简易征收',
                '按5%简易征收',
                '按5%简易征收减按1.5%计征',
                '稀土产品',
                '简易征收',
                '超税负12%即征即退',
                '超税负3%即征即退',
                '超税负8%即征即退',
            ],
            defaultProps: {
                label: 'mc',
                short_name: 'short_name',
                id: 'bm',
                children: 'children',
            },
            tax_code: '', //税收分类编码
            tax_short_name: '', //税收分类简称
            // tax_option: "", //规格型号
            tax_unit: '', //单位
            favorable_policy: '', //优惠政策
            is_favorable_policy: 0, //是否使用优惠政策
            free_of_tax: null, //1正常税率2出口免税和其他免税优惠政策3不征增值税4普通零税率
            tax_product_name: '', // 发票商品名称
            short_code: '', // 商品简码
            tax_measure_price: 0, //税收计量单价
            tax_rate: 0, //税率
            is_tax_logo: 1, //含税标志
            bill_position: 1, // 赋码方式 1=按商品 2按规格  新增部分
            /* 发票部分结束 */

            brand_id: '', // 品牌
            progressIsShow: false,
            progressNum: 0,
            videoUrl: '', // 首图视频
            // 刷新按钮状态
            btnLoadIsShow: false,
            // 运费模板id
            freight_template_id: null,
            // 运费模板option
            freightTemplateOption: [],
            // 商品图片排序dialog
            dialogSortIsShow: false,
            dialogSortIsShow2: false,
            drag: false,
            goodsImgList: [],
            // 批量input是否显示
            batchInputIsShow: false,
            // 批量input绑定值
            batchInputValue: '',
            batchPrecision: 0,
            batchValueName: '',
            path: this.$path,
            select: 1,
            // 规格显示
            speIsShow: true,
            step: 1,
            // tabs默认选中项
            activeName: '1',
            //------商品id-------//
            goodsId: 0,
            //------分类信息-------//
            classify: {},
            //------基本信息-------//
            classifyList1: [], //类目1
            classifyList2: [], //类目2
            classifyList3: [], //类目3
            classifyCheck1: null, // 选中类目1
            classifyCheck2: null, // 选中类目2
            classifyCheck3: null, // 选中类目3
            goodsClassifyStr: '',
            goodsSort: 0,
            goodsTitle: '',
            sn: '',
            barcode: '',
            code: '',
            goodsDesc: '',
            goodsImageUrl: '', //产品主图
            goodsGallery: [], //产品图册  - type 类型（1图片2视频） - src 资源链接
            goodsGalleryFileList: [],
            goodsSales: 0,
            goodsService: '',
            //运费模式类型
            // 0 统一运费 1 运费模板 2 第三方运费
            goodsFreightType: '0',
            goodsFreightPrice: 0,
            //------商品详情-------//
            // goodsDetailImages: [],
            goodsDetailImages: '',
            goodsDetailImagesFileList: [],
            //------商品参数-------//
            goodsParamList: [],
            //------销售属性-------//
            typeRadio: 1, // 属性类型 1=单规格 0=多规格
            goodsUnit: '',
            goodsUnitList: [], // 单位列表
            isUnit: true,
            goodsSn: '',
            goodsBarcode: '',
            goodsCode: '',
            goodsWeight: 0.0,
            goodsVolume: 0.0,
            goodsStock: 0,
            goodsPrice: 0.0,
            goodsOriginPrice: 0.0,
            goodsCostPrice: 0.0,
            goodsGuidePrice: 0.0, //商品指导价
            goodsActivityPrice: 0.0, //商品营销价
            //------商品特殊资质-------//
            goodsAffiche: [],
            is_display: 0,
            is_auto_update_cost: 0, // 商品自动更新成本价
            is_auto_update_price: 0, // 商品自动更新供货价
            //------批量处理-------//
            batchStock: 0,
            batchOriginPrice: 0.0,
            batchPrice: 0.0,
            batchCostPrice: 0.0,
            batchSn: '',
            batchBarcode: '',
            batchWeight: 0.0,
            batchVolume: 0.0,
            shop_level: 0,
            des_level: 0,
            express_level: 0,
            level: 0,
            // 视频号
            editData: [],
            //详情图片数组
            caogaoID: 0,
            detialimgsArr: [],
            productdetial_id: '',
            detialString: '',
            detialdialogSortIsShow: false,
            is_open: 0,
            is_videodisplay: 0,
            attr_key: [],
            videofreight_template_id: '',
            input1: '',
            productAmages: '',
            smallVideobrand_id: '',
            videogoodsImageUrl: '',
            //资质主图列表
            videogoodsImageUrlList: [],
            //商品详情图片列表
            detialproductphotoList: [],
            videoproductDetial: '',
            smallShopVideofreightgetData: [],
            checkList: [],
            smallClassifyList1: [], //类目1
            smallClassifyList2: [], //类目2
            smallClassifyList3: [], //类目3
            smallClassifyCheck1: null, // 选中类目1
            smallClassifyCheck2: null, // 选中类目2
            smallClassifyCheck3: null, // 选中类目3
            // 字符串类型数据
            productArgamentsString: [],
            //数组类型数据
            productArgamentsArr: [],
            //商品参数数据
            productArgamentsData: [],
            //储存下拉数组
            productcopyArr: [],
            // 视频号: 商品规格规范
            saleAttrList: [],
            // 是否需要
            shop_no_shipment: '',
            productArgamentsDatacopy: [],
            //规格项数据
            spec: [],
            skus: [],
            sku_id: null,
            arrayList: [],
            results: [],
            status_lock: 0, // 锁定状态  1锁定 0不锁定
            maxImgDialogIsShow: false,
            maxImgList: [],
            user_price_switch: 0, // 会员价独立开关
            user_price: {
                method: 1,
                levels: [],
            },
            gather_supply: {},
        };
    },
    watch: {
        // smallClassifyCheck3(){
        //     console.log('11232131')
        //     smallShopVideocategorydetail({'cat_id': this.smallClassifyCheck3}).then((res) => {
        //         if (res.code == 0) {
        //             console.log(res.data.detail.attr_list)
        //             // this.editData = []
        //             // this.productArgamentsData = []
        //             this.productArgamentsData = res.data.detail.attr_list
        //             this.productArgamentsData.forEach((item,index) => {
        //                 console.log(item)
        //                this.productArgamentsDatacopy.forEach((item2) => {
        //                    console.log(item2)
        //                    if(item.name === item2.name){
        //                        item.value = item2.value
        //                    }
        //                })
        //             })
        //             console.log(this.productArgamentsData)
        //         }
        //     })
        //
        // },
        typeRadio(val) {
            switch (
                val // 新增部分
            ) {
                case 1:
                    this.bill_position = 1;
                    break;
                case 0:
                    this.bill_position = 2;
                    break;
            }
            if (val === 1 && this.skus[0]) {
                let sku = this.skus[0];
                this.goodsPrice = sku.price;
                this.goodsOriginPrice = sku.origin_price;
                this.singleSkuImg = sku.image_url;
                this.goodsCostPrice = sku.cost_price;
                this.goodsGuidePrice = sku.guide_price;
                this.goodsActivityPrice = sku.activity_price;
                this.goodsStock = sku.stock;
                this.sn = sku.sn;
                this.barcode = sku.barcode;
                this.code = sku.code;
                this.goodsWeight = sku.weight;
            } else if (val === 0 && this.skus.length <= 1) {
                if (!this.skus[0]) {
                    this.addSpecItem('默认');
                    this.addSpecSubdataItem(0, '默认');
                    this.assemblySpec();
                }
                // this.skus[0].price = this.goodsPrice
                // this.skus[0].origin_price = this.goodsOriginPrice
                // this.skus[0].cost_price = this.goodsCostPrice
                // this.skus[0].guide_price = this.goodsGuidePrice
                // this.skus[0].activity_price = this.goodsActivityPrice
                // this.skus[0].stock = this.goodsStock
                // this.skus[0].sn = this.sn
                // this.skus[0].barcode = this.barcode
                // this.skus[0].weight = this.goodsWeight
            }
        },
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },

    mounted() {
        this.getUnit();
        //获取一级类目
        if (this.$route.query.is_plugin && this.$route.query.source) {
            getClassifySelect(1, 0).then((r) => {
                this.classifyList1 = r.data.list;
            });
        } else {
            getClassify(1, 0).then((r) => {
                this.classifyList1 = r.data.list;
            });
        }
        /* getClassify(1, 0).then((r) => {
            this.classifyList1 = r.data.list;
        }); */
        this.getBrandsOptios();
        // 获取会员等级
        this.getUserLevelList();
        //获取商品信息
        this.initProduct();
        /* //获取视频号一级类目
        getCategoriesByCondition({'f_cat_id': 0,'level':1}).then((r) => {
            this.smallClassifyList1 = r.data.categories;
        }); */
        // 获取运费模板
        this.getFreightTemp();
        /* //视频号运费模板
        this.smallShopVideofreightList() */
    },
    methods: {
        // 获取单位列表
        async getUnit() {
            const res = await getUnit();
            if (res.code === 0) {
                this.goodsUnitList = res.data.list;
            }
        },
        // 获取等级列表
        async getUserLevelList() {
            const params = {
                page: 1,
                pageSize: 99999,
            };
            const res = await getUserLevelList(params);
            if (res.code === 0) {
                res.data.list.forEach((item) => {
                    item.level_id = item.id;
                });
                this.user_price.levels = res.data.list;
            }
        },
        // 修改计算方式
        changeMethod() {
            this.user_price.levels.forEach((item) => {
                item.value = null;
            });
        },
        // 查看图片
        openMaxImgDialog(imgs) {
            this.maxImgDialogIsShow = true;
            this.maxImgList = imgs;
        },
        tipClose() {
            this.verifySkusPrice = true;
            this.isShow = false;
        },
        tipConfirm() {
            this.verifySkusPrice = false;
            this.isShow = false;
            this.submitGoods();
        },
        // 获取 视频号信息
        shipin() {
            this.editData = [];
            this.videogoodsImageUrlList = [];
            this.detialproductphotoList = [];

            smallShopVideoproductfindDraft({ product_id: this.goodsId }).then(
                (res) => {
                    if (res.code == 0) {
                        this.caogaoID = res.data.draft.id;
                        this.is_open = res.data.draft.is_video_shop;
                        this.smallClassifyCheck1 =
                            res.data.draft.wechat_product.cats[0].cat_id;
                        this.smallClassifyCheck2 =
                            res.data.draft.wechat_product.cats[1].cat_id;
                        this.smallClassifyCheck3 =
                            res.data.draft.wechat_product.cats[2].cat_id;
                        this.videofreight_template_id =
                            res.data.draft.wechat_product.express_info.template_id;
                        this.smallhandleFilter();
                        //获取视频号2级类目
                        getCategoriesByCondition({
                            f_cat_id: this.smallClassifyCheck1,
                            level: 2,
                        }).then((r) => {
                            this.smallClassifyList2 = r.data.categories;
                        });
                        //获取视频号3级类目
                        getCategoriesByCondition({
                            f_cat_id: this.smallClassifyCheck2,
                            level: 3,
                        }).then((r) => {
                            this.smallClassifyList3 = r.data.categories;
                        });
                        smallShopVideocategorydetail({
                            cat_id: this.smallClassifyCheck3,
                        }).then((res) => {
                            if (res.code == 0) {
                                this.productArgamentsData =
                                    res.data.detail.attr_list;
                                this.productArgamentsData.forEach(
                                    (item, index) => {
                                        if (item.type == 'string') {
                                            //字符串类型数组
                                            this.productArgamentsString.push(
                                                item,
                                            );
                                        }
                                        if (item.type == 'select_one') {
                                            //字符串类型数组
                                            this.productArgamentsArr.push(item);
                                        }
                                    },
                                );
                                this.productArgamentsData.forEach(
                                    (item, index) => {
                                        this.productArgamentsDatacopy.forEach(
                                            (item2) => {
                                                if (item.name === item2.name) {
                                                    item.value = item2.value;
                                                }
                                            },
                                        );
                                    },
                                );
                            }
                        });
                        this.is_videodisplay =
                            res.data.draft.wechat_product.deliver_method;
                        this.smallVideobrand_id =
                            res.data.draft.wechat_product.brand_id || '';
                        res.data.draft.wechat_product.attrs.forEach((item) => {
                            if (item.type == 'string') {
                                //数据储存
                                this.productArgamentsDatacopy.push({
                                    name: item.attr_key,
                                    value: item.attr_value,
                                });
                            }
                            if (item.type == 'select_one') {
                                this.editData.push(item.attr_value);
                            }
                        });
                        res.data.draft.wechat_product.qualifications.forEach(
                            (item) => {
                                this.videogoodsImageUrlList.push({
                                    src: item,
                                    url: item,
                                });
                            },
                        );
                        res.data.draft.wechat_product.desc_info.imgs.forEach(
                            (item) => {
                                this.detialproductphotoList.push({
                                    src: item,
                                    url: item,
                                });
                            },
                        );
                        console.log(this.detialproductphotoList);
                        this.videoproductDetial =
                            res.data.draft.wechat_product.desc_info.desc;
                    }
                },
            );
        },
        // 确认
        confirmPass() {
            this.checkList.forEach((item) => {
                this.detialproductphotoList.push({ url: item, src: item });
            });
            this.detialdialogSortIsShow = false;
            this.$message.success('提取成功');
        },
        handlePassClose() {
            this.detialdialogSortIsShow = false;
        },
        //视频号提交
        synchronizesubmit() {
            console.log(this.editData);
            console.log(this.productArgamentsData);
            console.log(this.productArgamentsArr);
            let productAmgents = [];
            this.productArgamentsData.forEach((item, index) => {
                if (item.type == 'string') {
                    productAmgents.push({
                        type: item.type,
                        attr_key: item.name,
                        attr_value: item.value,
                    });
                }
            });
            this.productArgamentsArr.forEach((item1, index1) => {
                console.log(item1, index1);
                this.editData.forEach((item2, index2) => {
                    console.log(item2, index2);
                    if (index1 === index2)
                        productAmgents.push({
                            type: item1.type,
                            attr_key: item1.name,
                            attr_value: String(item2),
                        });
                });
                console.log(productAmgents);
            });

            let imgArr = [];
            this.videogoodsImageUrlList.forEach((item, index) => {
                imgArr.push(item.url);
            });
            let detialimgArr = [];
            if (this.detialproductphotoList) {
                this.detialproductphotoList.forEach((item, index) => {
                    detialimgArr.push(item.url);
                });
            }
            let prams = {
                id: this.caogaoID,
                product_id: this.goodsId,
                is_video_shop: this.is_open,
                wechat_product: {
                    deliver_method: this.is_videodisplay,
                    desc_info: {
                        imgs: detialimgArr,
                        desc: this.videoproductDetial,
                    },
                    brand_id: String(this.smallVideobrand_id),
                    qualifications: imgArr,
                    cats: [
                        { cat_id: this.smallClassifyCheck1 },
                        { cat_id: this.smallClassifyCheck2 },
                        { cat_id: this.smallClassifyCheck3 },
                    ],
                    attrs: productAmgents,
                    express_info: {
                        template_id: String(this.videofreight_template_id),
                    },
                },
            };
            smallShopVideoproductsaveDraft(prams).then((res) => {
                if (res.code == 0) {
                    // this.$message.success(res.msg)
                    this.shipin();
                }
            });
        },
        //视频号同步分类
        synchronizeClick() {
            smallShopVideocategorysync().then((res) => {
                if (res.code == 0) {
                    this.$message.success(res.msg);
                }
            });
        },
        //同步品牌
        synchbrandClick() {
            smallShopVideoBrandneedSync().then((res) => {
                if (res.code == 0) {
                    this.$message.success(res.msg);
                }
            });
        },
        handleFilter(val) {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize,
                name: val,
            };
            getBrandsList(params).then((res) => {
                if (res.code === 0) {
                    this.brandsOptiosData.brandsOptios = res.data.list;
                    this.brandsOptiosData.total = res.data.total;
                }
            });
        },
        smallhandleFilter(val) {
            let params = {
                page: this.smallbrandsOptiosData.page,
                pageSize: this.smallbrandsOptiosData.pageSize,
                ch_name: val,
            };
            smallShopVideobrandlist(params).then((res) => {
                if (res.code === 0) {
                    this.smallbrandsOptiosData.brandsOptios = res.data.list;
                    this.smallbrandsOptiosData.total = res.data.total;
                }
            });
        },
        async getSourceOption() {
            const { code, data } = await getSupplierSourceOptionList({
                supplier_id: this.supplier_id,
            });
            if (code === 0) {
                this.source_options = data.list;
            }
        },
        async getSourceClassifyOption() {
            const { code, data } = await getSupplierSourceCategoryOptionList({
                supplier_id: this.supplier_id,
            });
            if (code === 0) {
                this.source_classify_options = data.list;
            }
        },
        handleBrandPage(val) {
            this.brandsOptiosData.page = val;
            this.getBrandsOptios();
        },
        smallhandleBrandPage(val) {
            this.smallbrandsOptiosData.page = val;
            this.smallgetBrandsOptios();
        },
        remoteMethod(query) {
            this.brandsOptiosData.name = query;
            this.brandsOptiosData.page = 1;
            this.getBrandsOptios();
        },
        async getBrandsOptios() {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize,
            };
            if (this.brandsOptiosData.name)
                params.name = this.brandsOptiosData.name;
            this.brandsOptiosData.loading = true;
            let res = await getBrandsList(params);
            this.brandsOptiosData.loading = false;
            if (res.code === 0) {
                console.log(res);
                this.brandsOptiosData.total = res.data.total;
                this.brandsOptiosData.brandsOptios = res.data.list;
                // this.brandsOptiosData.brandsOptios = res.data.list.sort(function (a, b) {
                //     return (a.name.spell().toLowerCase() - b.name.spell().toLowerCase())
                // })
                this.copyBrandOptios = Object.assign(
                    this.brandsOptiosData.brandsOptios,
                );
            }
        },
        async smallgetBrandsOptiospp() {
            console.log(11);
            let params = {
                page: this.smallbrandsOptiosData.page,
                pageSize: this.smallbrandsOptiosData.pageSize,
                ch_name: this.smallbrandsOptiosData.name,
            };
            // if (this.smallbrandsOptiosData.name) params.name = this.smallbrandsOptiosData.name
            this.smallbrandsOptiosData.loading = true;
            let res = await smallShopVideobrandlist(params);
            this.smallbrandsOptiosData.loading = false;
            if (res.code === 0) {
                console.log(res);
                this.smallbrandsOptiosData.total = res.data.total;
                this.smallbrandsOptiosData.brandsOptios = res.data.list;
                // this.brandsOptiosData.brandsOptios = res.data.list.sort(function (a, b) {
                //     return (a.name.spell().toLowerCase() - b.name.spell().toLowerCase())
                // })
                this.copyBrandOptios = Object.assign(
                    this.smallbrandsOptiosData.brandsOptios,
                );
            }
        },
        async smallgetBrandsOptios() {
            let params = {
                page: this.smallbrandsOptiosData.page,
                pageSize: this.smallbrandsOptiosData.pageSize,
            };
            if (this.smallbrandsOptiosData.name)
                params.name = this.smallbrandsOptiosData.name;
            this.smallbrandsOptiosData.loading = true;
            let res = await smallShopVideobrandlist(params);
            this.smallbrandsOptiosData.loading = false;
            if (res.code === 0) {
                console.log(res);
                this.smallbrandsOptiosData.total = res.data.total;
                this.smallbrandsOptiosData.brandsOptios = res.data.list;
                // this.brandsOptiosData.brandsOptios = res.data.list.sort(function (a, b) {
                //     return (a.name.spell().toLowerCase() - b.name.spell().toLowerCase())
                // })
                this.copyBrandOptios = Object.assign(
                    this.smallbrandsOptiosData.brandsOptios,
                );
            }
        },
        //获取商品参数
        getproductargments(id) {
            console.log(id);
            this.productArgamentsData = [];
            this.editData = [];
            this.productArgamentsArr = [];
            smallShopVideocategorydetail({ cat_id: id }).then((res) => {
                if (res.code == 0) {
                    this.productArgamentsData = res.data.detail.attr_list;
                    this.saleAttrList = res.data.detail.sale_attr_list;
                    this.productArgamentsData.forEach((item, index) => {
                        if (item.type == 'string') {
                            //字符串类型数组
                            this.productArgamentsString.push(item);
                        }
                        if (item.type == 'select_one') {
                            //字符串类型数组
                            this.productArgamentsArr.push(item);
                        }
                    });
                    this.shop_no_shipment = res.data.detail.shop_no_shipment;
                    if (this.shop_no_shipment == true) {
                        this.is_videodisplay = 1;
                    }
                }
            });
        },
        // 获取品牌option
        /*async getBrandsOpstios() {
            let res = await getBrandOptionList()
            if (res.code === 0) {
                this.brandOptios = res.data.list
            }
        },*/
        handleTabClick(tab) {
            if (tab.name === '6' && this.bill_position === 1) {
                // 获取发票分类
                this.getBillClassify();
            }
            if (tab.name === '2') {
                this.$refs.mEditor.doNotPastePictures();
            }
        },
        /* 发票部分开始 */
        resSkus(skus) {
            // 新增部分
            this.skus = skus;
            skus.forEach((item, index) => {
                this.$set(this.skus, index, {
                    ...skus[index],
                });
            });
        },
        openCodeTypeDialog(type = '') {
            // 新增部分
            this.$refs.codeTypeDialog.isShow = true;
            this.$refs.codeTypeDialog.getBillClassify();
            this.$nextTick(() => {
                this.$refs.codeTypeDialog.goodsTitle = this.goodsTitle;
                this.$refs.codeTypeDialog.productInfo = this.productInfo;
                this.$refs.codeTypeDialog.skus = this.skus;
                this.$refs.codeTypeDialog.type = type;
                if (typeof type === 'number') {
                    this.$refs.codeTypeDialog.setForm();
                }
            });
        },
        // 过滤tree
        searchTree() {
            this.$refs.tree.filter(this.treeInput);
        },
        filterNode(value, data, node) {
            if (!value) {
                node.expanded = false;
                return true;
            }
            let val = value.toLowerCase();
            return this.chooseNode(val, data, node);
            /*if (!value) return true;
            return data.mc.indexOf(value) !== -1;*/
        },
        chooseNode(value, data, node) {
            if (data.mc.indexOf(value) !== -1) {
                return true;
            }
            const level = node.level;
            if (level === 1) {
                return false;
            }
            let parentData = node.parent;
            let index = 0;
            while (index < level - 1) {
                if (parentData.data.mc.indexOf(value) !== -1) {
                    return true;
                }
                parentData = parentData.parent;
                index++;
            }
            return false;
        },
        // 选中发票分类
        handleNodeClick(data) {
            if (!data.children) {
                let value = parseInt(data.zzssl.split('%')[0]);
                this.taxRateOptios = [
                    // 税率optios
                    { label: '0%', value: 0 },
                    { label: '1%', value: 1 },
                    { label: '3%', value: 3 },
                    { label: '6%', value: 6 },
                    { label: '9%', value: 9 },
                    { label: '10%', value: 10 },
                    { label: '11%', value: 11 },
                    { label: '13%', value: 13 },
                    { label: '17%', value: 17 },
                ];
                if (this.taxRateOptios.indexOfJSON('value', value) === -1) {
                    this.taxRateOptios.push({
                        label: data.zzssl,
                        value: value,
                    });
                }
                this.tax_rate = value;
                this.tax_code = data.bm;
                this.tax_short_name = data.spbmjc;
            }
        },
        // 获取发票分类
        async getBillClassify() {
            let res = await getBillCategory();
            if (res.code === 0) {
                this.treeData = res.data.list;
            }
        },
        /* 发票部分结束 */

        // 编辑描述dialog回调
        getDescribe(data) {
            this.skus[data.index].describe = data.describe;
            if (data.title) {
                this.skus[data.index].title = data.title;
            }
            this.skus[data.index].desc = data.desc ? data.desc : '';
            this.skus[data.index].video_url = data.video_url
                ? data.video_url
                : '';
            if (data.image_url) {
                // this.skus[data.index].image_url = data.image_url;
                this.$set(this.skus, data.index, {
                    ...this.skus[data.index],
                    image_url: data.image_url,
                });
            }
            if (data.attrs.length > 0) {
                this.skus[data.index].attrs = data.attrs;
            } else {
                this.skus[data.index].attrs = [];
            }
            if (data.gallery.length > 0) {
                this.skus[data.index].gallery = data.gallery;
            } else {
                this.skus[data.index].gallery = [];
            }
        },
        editDescribe(item, index) {
            this.$refs.SkusIntroduce.isShow = true;
            this.$refs.SkusIntroduce.setData(item, index);
        },
        handle9Exceed(files, fileList) {
            this.$message.warning(
                `当前限制选择 9 个图片，本次选择了 ${
                    files.length
                } 个图片，超出了 ${files.length + fileList.length - 9} 个图片`,
            );
        },
        addTemp() {
            this.$refs.tempDialog.isShow = true;
            this.$nextTick(() => {
                this.$refs.tempDialog.title = '新增';
            });
        },
        // 获取运费模板
        getFreightTemp(flg = false) {
            this.btnLoadIsShow = flg;
            getExpressTemplateList({ page: 1, pageSize: 999 }).then((res) => {
                if (res.code === 0) {
                    this.freightTemplateOption = res.data.list;
                    this.btnLoadIsShow = false;
                }
            });
        },
        //视频号运费模板
        smallShopVideofreightList(flg = false) {
            this.btnLoadIsShow = flg;
            smallShopVideofreightget({ page: 1, pageSize: 999 }).then((res) => {
                if (res.code === 0) {
                    this.smallShopVideofreightgetData = res.data.freights;
                    this.btnLoadIsShow = false;
                }
            });
        },
        //同步运费模板
        synctemplateClick() {
            smallShopVideofreightsync().then((res) => {
                if (res.code == 0) {
                    this.$message.success(res.msg);
                }
            });
        },
        // 关闭排序dialog
        dialogSortClose() {
            this.dialogSortIsShow = false;
        },
        detialdialogSortClose() {
            this.detialdialogSortIsShow = false;
        },
        dialogSortClose2() {
            this.dialogSortIsShow2 = false;
        },
        // 打开排序dialog
        openDialogSort(type) {
            if (type === 1) {
                if (this.goodsGallery.length > 0) {
                    this.dialogSortIsShow = true;
                } else {
                    this.$message.error('请上传图片');
                }
            } else if (type === 2) {
                if (this.goodsDetailImages.length > 0) {
                    this.dialogSortIsShow2 = true;
                } else {
                    this.$message.error('请上传图片');
                }
            }
        },
        // 提取详情图片
        detailedimages() {
            this.detialdialogSortIsShow = true;
            smallShopVideoproductgetImgs({ product_id: this.goodsId }).then(
                (res) => {
                    if (res.code == 0) {
                        console.log(res);
                        this.detialimgsArr = res.data.imgs;
                    }
                },
            );
        },
        //开始拖拽事件
        onStart() {
            this.drag = true;
        },
        //拖拽结束事件
        onEnd(type) {
            this.drag = false;
            this.$message.success('排序成功!');

            switch (type) {
                case 1:
                    this.goodsGalleryFileList = [...this.goodsGallery];
                    break;
                case 2:
                    this.goodsDetailImagesFileList = [
                        ...this.goodsDetailImages,
                    ];
                    break;
            }
        },
        // 将品牌的第一条数据隐藏掉
        changeSelect() {
            this.showSelect = false;
        },
        //获取商品信息
        initProduct() {
            if (!this.$route.query.id) {
                return;
            }
            findProduct({ id: this.$route.query.id }).then((res) => {
                if (res.code == 0) {
                    // AI赋码用
                    this.productInfo = res.data.reproduct;
                    this.brand_name = res.data.reproduct.brand.name;
                    this.detialString = res.data.reproduct.detail_images;
                    this.user_price_switch =
                        res.data.reproduct.user_price_switch;
                    if (res.data.reproduct.user_price.levels) {
                        res.data.reproduct.user_price.levels =
                            this.user_price.levels.map((item1) => {
                                let item2 =
                                    res.data.reproduct.user_price.levels.find(
                                        (item) => item.level_id === item1.id,
                                    );
                                if (item2) {
                                    return {
                                        ...item1,
                                        value: item2.value / 100,
                                    };
                                } else {
                                    return item1;
                                }
                            });
                    } else {
                        res.data.reproduct.user_price.levels =
                            this.user_price.levels;
                    }
                    this.user_price = res.data.reproduct.user_price;
                    this.recoverGoods(res.data.reproduct);
                    if (res.data.plugin_widget.length !== 0) {
                        this.plugin_widget = res.data.plugin_widget[0].id;
                        // 获取当前商品视频号草稿箱
                        this.shipin();
                        //获取视频号一级类目
                        getCategoriesByCondition({
                            f_cat_id: 0,
                            level: 1,
                        }).then((r) => {
                            this.smallClassifyList1 = r.data.categories;
                        });
                        //视频号运费模板
                        this.smallShopVideofreightList();
                    }
                } else {
                }
            });
        },
        //显示批量处理input
        openatchInput(valueName) {
            this.batchInputIsShow = true;
            this.batchValueName = valueName;
            if (valueName === 'batchStock') {
                this.batchPrecision = 0;
            } else if (valueName === 'batchVolume') {
                this.batchPrecision = 3;
            } else {
                this.batchPrecision = 2;
            }
        },
        // 确定
        confirmBatch() {
            this[this.batchValueName] = this.batchInputValue;
            this.skusBatch();
        },
        // 取消
        cancelBatch() {
            this.batchInputIsShow = false;
            this.batchValueName = '';
            this.batchInputValue = '';
        },
        // 获取二三级类目
        async getClassify2o3(level, pid) {
            if (level === 3) {
                this.classifyList3 = [];
                this.classifyCheck3 = null;
            } else {
                this.classifyList2 = [];
                this.classifyCheck2 = null;
                this.classifyList3 = [];
                this.classifyCheck3 = null;
            }
            let r = '';
            if (this.$route.query.is_plugin && this.$route.query.source) {
                r = await getClassifySelect(level, pid);
            } else {
                r = await getClassify(level, pid);
            }
            // r = await getClassify(level, pid);
            switch (level) {
                case 2:
                    this.classifyList2 = r.data.list;
                    break;
                case 3:
                    this.classifyList3 = r.data.list;
                    break;
            }
        },
        // 获取视频号二三级类目
        async smallGetClassify2o3(level, pid) {
            if (level === 3) {
                this.smallClassifyList3 = [];
                this.smallClassifyCheck3 = null;
            } else {
                this.smallClassifyList2 = [];
                this.smallClassifyCheck2 = null;
                this.smallClassifyList3 = [];
                this.smallClassifyCheck3 = null;
            }
            let r = await getCategoriesByCondition({
                f_cat_id: pid,
                level: level,
            });
            switch (level) {
                case 2:
                    this.smallClassifyList2 = r.data.categories;
                    break;
                case 3:
                    this.smallClassifyList3 = r.data.categories;
                    break;
            }
        },
        // 单规格图片
        handleSingSkuImgSuccess(res) {
            if (res.code === 0) {
                this.singleSkuImg = res.data.file.url;
            }
        },
        // 商品相册图片
        handleGoodsGallerySuccess(res) {
            if (res.code === 0) {
                this.goodsGallery.push({
                    type: 1,
                    src: res.data.file.url,
                    url: res.data.file.url,
                });
                this.goodsGalleryFileList = [...this.goodsGallery];
            } else {
                this.$message.error(res.msg);
            }
        },
        // 商品详情 图片
        detialhandleGoodsGallerySuccess(res) {
            if (res.code === 0) {
                this.detialproductphotoList.push({
                    type: 1,
                    src: res.data.file.url,
                    url: res.data.file.url,
                });
                // this.goodsGalleryFileList = [...this.goodsGallery];
            } else {
                this.$message.error(res.msg);
            }
        },
        // 资质主图相册
        zizhihandleGoodsGallerySuccess(res) {
            console.log(res);
            if (res.code === 0) {
                this.videogoodsImageUrlList.push({
                    url: res.data.file.url,
                });
                // this.goodsGalleryFileList = [...this.goodsGallery];
            } else {
                this.$message.error(res.msg);
            }
        },
        // 上传首图视频
        handleMainVideoSuccess(res) {
            if (res.code === 0) {
                this.progressIsShow = false;
                this.videoUrl = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        videoPrigress(event, file, fileList) {
            this.progressNum = 0;
            this.progressIsShow = true;
            if (Math.floor(event.percent) >= 100) {
                setTimeout(() => {
                    this.progressNum = 99;
                }, 500);
            } else {
                setTimeout(() => {
                    this.progressNum = Math.floor(event.percent);
                }, 500);
            }
        },
        removeVideo() {
            this.videoUrl = '';
        },
        videoError(err, file, fileList) {
            this.progressNum = 0;
            this.progressIsShow = true;
            this.$message.error('上传失败');
        },
        // 删除商品相册图片
        removeGoodsGalleryList(index) {
            console.log(index);
            this.goodsGallery.splice(index, 1);
            this.goodsGalleryFileList.splice(index, 1);
            /*this.goodsGallery = [];
            fileList.forEach(item => {
                this.goodsGallery.push({
                    type: 1,
                    src: item.url,
                    url: item.url
                })
            })*/
        },
        // 删除商品详情图片
        xiangqingremoveGoodsGalleryList(index) {
            console.log(index);
            this.detialproductphotoList.splice(index, 1);
            /*this.goodsGallery = [];
            fileList.forEach(item => {
                this.goodsGallery.push({
                    type: 1,
                    src: item.url,
                    url: item.url
                })
            })*/
        },
        // 删除资质图片
        zizhiremoveGoodsGalleryList(index) {
            console.log(index);
            this.videogoodsImageUrlList.splice(index, 1);
            /*this.goodsGallery = [];
            fileList.forEach(item => {
                this.goodsGallery.push({
                    type: 1,
                    src: item.url,
                    url: item.url
                })
            })*/
        },
        // 上传主图
        handleMainImgSuccess(res) {
            if (res.code === 0) {
                this.goodsImageUrl = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        //上传视频号资质主图
        videohandleMainImgSuccess(res) {
            if (res.code === 0) {
                this.videogoodsImageUrl = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 商品详情
        handleGoodsDetailImagesSuccess(res) {
            this.goodsDetailImages.push({
                url: res.data.file.url,
            });
        },
        // 删除商品详情图片
        removeGoodsDetailImagesList(file, fileList) {
            this.goodsDetailImages = [];
            fileList.forEach((item) => {
                this.goodsDetailImages.push({
                    type: 1,
                    src: item.url,
                    url: item.url,
                });
            });
        },
        // 上传特殊资质名称
        handleGoodsAfficheSuccess(res) {
            if (res.code === 0) {
                this.goodsAffiche[res.data.file.index].url = res.data.file.url;
                this.goodsAffiche[res.data.file.index].src = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 移除特殊资质item
        removeItem(index) {
            this.goodsAffiche.splice(index, 1);
        },
        //添加特殊资质item
        addGoodsAffiche() {
            this.goodsAffiche.push({
                title: '',
                url: '',
            });
        },
        //初始化分类选项
        initClassifyTitle(classify) {
            let that = this;
            that.classify = classify;
            that.goodsClassifyStr =
                classify.category1_name +
                '/' +
                classify.category2_name +
                '/' +
                classify.category3_name;
        },
        //添加规格项数据
        addSpecByName(name) {
            let newSpec = {};
            newSpec.name = name;
            newSpec.options = [];
            return newSpec;
        },

        //添加规格项
        addSpecItem(name) {
            let that = this;
            let newSpec = that.addSpecByName(name);
            that.spec.push(newSpec);
        },

        //删除规格项
        deleteSpecItem(index) {
            let that = this;
            if (that.spec.length > 0) {
                that.spec.splice(index, 1);
            }
            that.assemblySpec();
        },

        //添加规格项子数据
        addSpecSubdataByName(name) {
            let newSpecSubdata = {};
            newSpecSubdata.name = name.replace(/\/pj\/\^/g, '+');
            return newSpecSubdata;
        },

        //添加规格项子数据
        addSpecSubdataItem(index, name) {
            let that = this;
            let tempSpec = that.spec[index];
            tempSpec.options.push(that.addSpecSubdataByName(name));
            that.spec[index] = tempSpec;
        },

        //更新规格项子数据
        updateSpecSubdataItemByNames(index, names) {
            let that = this;
            let tempSpec = that.spec[index];
            let tempOptions = [];
            names.forEach((name) => {
                tempOptions.push(that.addSpecSubdataByName(name));
            });
            tempSpec.options = tempOptions;
            that.spec[index] = tempSpec;
        },

        //删除规格项子数据
        deleteSpecSubdataItem(specIndex, specSubdataIndex) {
            let that = this;
            that.spec[specIndex].options.splice(specSubdataIndex, 1);
            if (
                that.spec[specIndex].options.length == 0 ||
                !that.spec[specIndex].options
            ) {
                that.spec.splice(specIndex, 1);
            }
            that.assemblySpec();
        },

        //规格名验证
        specNameVerify(specIndex, name) {
            let that = this;
            if (that.spec.length == 1 || !name) {
                that.assemblySpec();
                return;
            }
            for (let i = 0; i < that.spec.length; i++) {
                if (that.spec[i].name == name && i != specIndex) {
                    that.spec[specIndex].name = '';
                    that.$message.error('已经添加了相同的规格名');
                    return;
                }
            }
            that.assemblySpec();
        },

        //规格项验证
        specOptionNameVerify(specIndex, specSubdataIndex, name) {
            let that = this;
            if (that.spec[specIndex].options.length == 1 || !name) {
                that.assemblySpec();
                return;
            }
            for (let i = 0; i < that.spec[specIndex].options.length; i++) {
                if (
                    that.spec[specIndex].options[i].name == name &&
                    i != specSubdataIndex
                ) {
                    that.spec[specIndex].options[specSubdataIndex].name = '';
                    that.$message.error('已经添加了相同的规格项名');
                    return;
                }
            }
            that.assemblySpec();
        },

        //规格项验证
        verifySpec() {
            let that = this;
            if (that.spec.length == 0) {
                that.skus = [];
                that.$message.error('请添加规格');
                return false;
            }
            return true;
        },

        //skus验证
        verifySkus() {
            let that = this;
            if (that.skus.length == 0) {
                that.$message.error('请添加规格');
                return false;
            }
            // 判断规格名是否为空
            let isSpecName = that.spec.some((item) => !item.name);
            if (isSpecName) {
                that.$message.error('请填写规格名');
                return false;
            }
            return true;
        },

        //组装数据
        assemblySpec() {
            let that = this;
            if (!that.verifySpec()) {
                return;
            }
            let legacyData = this.$fn.deepClone(that.skus);
            that.skus = [];
            that.arrayList = [];
            that.results = [];

            for (let i = 0; i < that.spec.length; i++) {
                if (that.spec[i].options.length > 0) {
                    let temp = that.filterOptions(that.spec[i].options);
                    if (temp.length > 0) {
                        that.arrayList.push(
                            that.filterOptions(that.spec[i].options),
                        );
                    }
                }
            }
            that.forIn(0, null);
            that.results.forEach((item) => {
                that.skus.push(that.assemblySku(item));
            });
            if (
                that.skus.length &&
                legacyData.length &&
                that.skus.length === legacyData.length
            ) {
                that.skus.forEach((item, index) => {
                    item.image_url = legacyData[index].image_url;
                    item.stock = legacyData[index].stock;
                    item.origin_price = legacyData[index].origin_price;
                    item.price = legacyData[index].price;
                    item.cost_price = legacyData[index].cost_price;
                    item.guide_price = legacyData[index].guide_price;
                    item.activity_price = legacyData[index].activity_price;
                    item.weight = legacyData[index].weight;
                    item.sn = legacyData[index].sn;
                    item.barcode = legacyData[index].barcode;
                    item.code = legacyData[index].code;
                    item.describe = legacyData[index].describe;
                });
            }
            that.skus.forEach((item) => {
                legacyData.forEach((item2) => {
                    //  || item.id === item2.id
                    if (item.optionName === item2.optionName) {
                        // image_url  stock  origin_price  price cost_price guide_price activity_price weight sn barcode describe
                        item.id = item2.id;
                        item.image_url = item2.image_url;
                        item.stock = item2.stock;
                        item.origin_price = item2.origin_price;
                        item.price = item2.price;
                        item.cost_price = item2.cost_price;
                        item.guide_price = item2.guide_price;
                        item.activity_price = item2.activity_price;
                        item.weight = item2.weight;
                        item.sn = item2.sn;
                        item.barcode = item2.barcode;
                        item.code = item2.code;
                        item.describe = item2.describe;
                    }
                });
            });
        },

        //过滤Options
        filterOptions(options) {
            for (let i = 0; i < options.length; i++) {
                if (
                    options[i] == null ||
                    options[i].name == null ||
                    options[i].name == '' ||
                    options[i].name == undefined
                ) {
                    options.splice(i, 1);
                }
            }
            return options;
        },

        forIn(index, subContext) {
            let that = this;
            let stringList = that.arrayList[index];
            for (let i in stringList) {
                let e = stringList[i].name;
                let newE = stringList[i].name;
                let viewE = stringList[i].name;
                if (e) {
                    if (index !== 0) {
                        e = subContext + '/pj/^' + e;
                        viewE =
                            subContext.replace(/\+/g, '/pj/^') +
                            '+' +
                            viewE.replace(/\+/g, '/pj/^');
                        newE = subContext + '+' + newE;
                    } else {
                        viewE = e.replace(/\+/g, '/pj/^');
                    }
                    if (index === that.arrayList.length - 1) {
                        that.results.push({ code: e, str: newE, view: viewE });
                    } else {
                        that.forIn(index + 1, e);
                    }
                }
            }
        },

        //组装sku 单项数据 提交
        assemblySku(val) {
            let that = this;
            let skuItem = {};
            skuItem.optionName = val.view;
            skuItem.title = val.str;
            skuItem.sn = '';
            skuItem.barcode = '';
            skuItem.code = '';
            skuItem.weight = 0.0;
            skuItem.volume = 0.0;
            skuItem.price = 0.0;
            skuItem.cost_price = 0.0;
            skuItem.origin_price = 0.0;
            skuItem.guide_price = 0.0;
            skuItem.stock = 0;
            skuItem.original_sku_id = '0'; // 新SKU的original_sku_id默认为字符串'0'

            skuItem.spec_items = [];
            for (let i = 0; i < that.spec.length; i++) {
                let specItem = {};
                specItem.id = 0;
                specItem.value = that.getRowNameDecode(i, val.code);
                // specItem.value = val;
                specItem.spec_id = 0;
                let spec = {};
                spec.id = 0;
                spec.title = that.spec[i].name;
                specItem.spec = spec;
                skuItem.spec_items.push(specItem);
            }
            return skuItem;
        },
        getRowNameDecode(index, item) {
            if (item) {
                let arr = item.split('/pj/^');
                return arr[index];
            }
            return '';
        },
        getViewName(index, item) {
            if (item) {
                let arr = item.split('+');
                if (arr[index]) {
                    return arr[index].replace(/\/pj\/\^/g, '+');
                } else {
                    return arr[index];
                }
            }
            return '';
        },
        //获取rowName
        getRowName(index, item) {
            if (item) {
                let arr = item.split('+');
                return arr[index];
            }
            return '';
        },
        // 添加属性
        addGoodsParam() {
            this.goodsParamList.push({ name: '', value: '' });
        },
        // 删除属性
        delGoodsParam(index) {
            this.goodsParamList.splice(index, 1);
        },

        //提交商品
        submitGoods() {
            let that = this;
            if (!that.verifyGoods()) {
                return;
            }

            if (this.typeRadio === 0 && this.user_price.method === 2) {
                this.$message.error('多规格商品不支持会员价');
                return;
            }
            this.verifySkusPrice = true;
            let goodsJson = {
                id: that.goodsId,
                //
                category1_id: that.classifyCheck1,
                category2_id: that.classifyCheck2,
                category3_id: that.classifyCheck3,
                //
                sort: that.goodsSort,
                title: that.goodsTitle,
                sn: that.sn,
                barcode: that.barcode,
                code: that.code,
                desc: that.goodsDesc,
                image_url: that.goodsImageUrl,
                video_url: that.videoUrl,
                gallery: that.goodsGallery,
                sales: parseInt(that.goodsSales),
                service: that.goodsService, //待定字段
                freight_type: parseInt(that.goodsFreightType),
                freight: that.$changeMoneyY2F(that.goodsFreightPrice),
                freight_template_id: that.freight_template_id,
                //
                // "detail_images": that.transformGoodsDetailImages(),
                detail_images: that.goodsDetailImages,
                //
                attrs: that.goodsParamList,
                //
                unit: that.goodsUnit,
                min_buy_qty: that.minBuyQty,
                skus: [],
                //
                qualifications: that.goodsAffiche,
                shop_level: this.shop_level,
                des_level: this.des_level,
                express_level: this.express_level,
                level: this.level,
                single_option: this.typeRadio,
                is_display: this.is_display,
                status_lock: this.status_lock,
                // 发票
                /*"tax_code": that.tax_code,
                "tax_short_name": that.tax_short_name,
                "tax_unit": that.goodsUnit,
                "favorable_policy": that.favorable_policy,
                "is_favorable_policy": that.is_favorable_policy,
                "free_of_tax": that.free_of_tax,
                "short_code": that.short_code,
                "tax_measure_price": that.$changeMoneyY2F(that.goodsPrice),
                "tax_rate": that.tax_rate,
                "is_tax_logo": that.is_tax_logo,*/
                bill_position: that.bill_position, // 新增部分
                user_price_switch: this.user_price_switch, // 会员价独立开关
            };
            if (this.gather_supply.category_id == 2) {
                goodsJson.is_auto_update_cost = this.is_auto_update_cost;
                goodsJson.is_auto_update_price = this.is_auto_update_price;
            }
            // 会员价设置
            if (this.user_price.levels.length !== 0) {
                this.user_price.levels.forEach((item) => {
                    item.value = item.value * 100;
                });
                goodsJson.user_price = this.user_price;
            }
            if (this.supplier_id) {
                if (this.source_id) {
                    goodsJson.supplier_source_id = this.source_id;
                }
                if (this.source_classify_id) {
                    goodsJson.supplier_source_category_id =
                        this.source_classify_id;
                }
            }

            // 新增部分
            if (that.bill_position === 1) {
                goodsJson.tax_code = that.tax_code;
                goodsJson.tax_short_name = that.tax_short_name;
                goodsJson.tax_unit = that.goodsUnit;
                goodsJson.favorable_policy = that.favorable_policy;
                goodsJson.is_favorable_policy = that.is_favorable_policy;
                goodsJson.free_of_tax = that.free_of_tax;
                goodsJson.short_code = that.short_code;
                goodsJson.tax_measure_price = that.$changeMoneyY2F(
                    that.goodsPrice,
                );
                goodsJson.tax_rate = that.tax_rate;
                goodsJson.is_tax_logo = that.is_tax_logo;
                goodsJson.tax_product_name = that.tax_product_name;
            }
            if (this.brand_id) {
                goodsJson.brand_id = this.brand_id;
            }
            if (that.typeRadio === 1) {
                goodsJson.skus = [];
                // goodsJson.sn = that.goodsSn;
                // goodsJson.barcode = that.barcode;
                goodsJson.volume = that.goodsVolume;
                goodsJson.price = that.$changeMoneyY2F(that.goodsPrice);
                goodsJson.origin_price = that.$changeMoneyY2F(
                    that.goodsOriginPrice,
                );
                goodsJson.sku_image = that.singleSkuImg;
                goodsJson.cost_price = that.$changeMoneyY2F(
                    that.goodsCostPrice,
                );
                goodsJson.guide_price = that.$changeMoneyY2F(
                    that.goodsGuidePrice,
                );
                goodsJson.activity_price = that.$changeMoneyY2F(
                    that.goodsActivityPrice,
                );
                goodsJson.stock = that.goodsStock;
                goodsJson.sku_id = that.sku_id;
                goodsJson.weight = that.goodsWeight;
                goodsJson.original_sku_id = that.original_sku_id;
            } else {
                goodsJson.price = that.$changeMoneyY2F(that.goodsPrice);
                goodsJson.origin_price = that.$changeMoneyY2F(
                    that.goodsOriginPrice,
                );
                goodsJson.cost_price = that.$changeMoneyY2F(
                    that.goodsCostPrice,
                );
                goodsJson.guide_price = that.$changeMoneyY2F(
                    that.goodsGuidePrice,
                );
                goodsJson.activity_price = that.$changeMoneyY2F(
                    that.goodsActivityPrice,
                );
                goodsJson.stock = that.goodsStock;
                goodsJson.skus = that.transformSkusY2F();
            }
            console.log('goodsJson:', goodsJson);
            that.$loading({ text: '提交中...' });
            if (that.goodsId == 0) {
                createProduct(goodsJson).then((res) => {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.$loading().close();
                        that.$router.back();
                        that.changePrice();
                    } else {
                        that.$message.error(res.msg);
                        that.$loading().close();
                    }
                });
            } else {
                updateProduct(goodsJson).then((res) => {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.synchronizesubmit();
                        that.$loading().close();
                        that.changePrice();
                        // that.initProduct();
                        // that.$router.back();
                    } else {
                        that.$message.error(res.msg);
                        that.$loading().close();
                    }
                });
            }
        },

        // 价格除100
        changePrice() {
            this.user_price.levels.forEach((item) => {
                item.value = item.value / 100;
            });
        },

        //转换格式
        transformGoodsDetailImages() {
            let goodsDetailImagesPara = [];
            this.goodsDetailImages.forEach((item) => {
                goodsDetailImagesPara.push(item.url);
            });
            return goodsDetailImagesPara;
        },

        //元转分sku
        transformSkusY2F() {
            let that = this;
            let newSkus = [];
            that.skus.forEach((sku) => {
                let tempSku = { ...sku };
                tempSku.price = that.$changeMoneyY2F(tempSku.price);
                tempSku.cost_price = that.$changeMoneyY2F(tempSku.cost_price);
                tempSku.origin_price = that.$changeMoneyY2F(
                    tempSku.origin_price,
                );
                tempSku.guide_price = that.$changeMoneyY2F(tempSku.guide_price);
                tempSku.activity_price = that.$changeMoneyY2F(
                    tempSku.activity_price,
                );
                newSkus.push(tempSku);
            });
            return newSkus;
        },
        //验证字段
        verifyGoods() {
            let that = this;
            /* if (!that.classifyCheck1 || !that.classifyCheck2 || !that.classifyCheck3) {
                that.$message.error("请选择分类");
                return false;
            } */
            if (!that.classifyCheck1) {
                that.$message.error('请选择分类');
                return false;
            }
            if (!that.goodsTitle) {
                that.$message.error('请填写商品名称');
                return false;
            }
            if (!this.goodsImageUrl) {
                that.$message.error('请上传商品主图');
                return false;
            }
            if (this.goodsGallery.length <= 0) {
                that.$message.error('请上传商品图片');
                return false;
            }
            if (!this.goodsFreightType) {
                that.$message.error('请设置运费');
                return false;
            }

            if (this.goodsFreightType == '0') {
                if (
                    this.goodsFreightPrice === '' ||
                    this.goodsFreightPrice === '' ||
                    this.goodsFreightPrice == undefined
                ) {
                    that.$message.error('请填写统一运费');
                    return false;
                }
            } else if (this.goodsFreightType == '1') {
                if (!this.freight_template_id) {
                    that.$message.error('请选择运费模版');
                    return false;
                }
            }
            if (!that.goodsUnit) {
                that.$message.error('请填写商品单位');
                return false;
            }

            if (that.typeRadio === 1) {
                if (!that.verifyPrice(that.goodsPrice)) {
                    that.$message.error('请填写商品供货价');
                    return false;
                }

                if (!that.verifyPrice(that.goodsOriginPrice)) {
                    that.$message.error('请填写建议零售价');
                    return false;
                }

                if (!that.verifyPrice(that.goodsCostPrice)) {
                    that.$message.error('请填写商品成本价');
                    return false;
                }

                if (!that.verifyPrice(that.goodsGuidePrice)) {
                    that.$message.error('请填写商品指导价');
                    return false;
                }

                if (!that.verifyPrice(that.goodsActivityPrice)) {
                    that.$message.error('请填写商品营销价');
                    return false;
                }

                if (!that.verifyPrice(that.goodsStock)) {
                    that.$message.error('请填写商品库存');
                    return false;
                }

                if (!that.verifyPrice(that.goodsWeight)) {
                    that.$message.error('请填写重量');
                    return false;
                }
            }
            if (that.typeRadio === 0) {
                if (!that.verifySkus()) {
                    return false;
                }
                if (this.verifySkusPrice) {
                    let priceRes = that.skus.find(
                        (item) =>
                            !item.origin_price ||
                            !item.price ||
                            !item.cost_price ||
                            !item.guide_price,
                    );
                    if (priceRes) {
                        this.isShow = true;
                        return false;
                    }
                }
            }
            if (that.bill_position === 1 && that.tax_code) {
                // 新增部分
                if (that.tax_rate === '') {
                    that.$message.error('商品未赋码请选择税率');
                    return false;
                }
                if (that.is_favorable_policy === 1 && !that.favorable_policy) {
                    that.$message.error('商品未赋码请选择优惠政策类型');
                    return false;
                }
                if ([1, 2, 3, 4].indexOf(that.free_of_tax) === -1) {
                    that.$message.error('商品未赋码请选择免税类型');
                    return false;
                }
            }
            // 新增部分
            let flg = true;
            if (that.bill_position === 2) {
                for (let i = 0; i < that.skus.length; i++) {
                    if (that.skus[i].tax_code) {
                        if (
                            typeof that.skus[i].tax_rate === 'undefined' ||
                            that.skus[i].tax_rate === ''
                        ) {
                            that.$message.error(
                                `[${that.skus[i].title}] 规格未赋码请选择税率`,
                            );
                            flg = false;
                            break;
                        }
                        if (
                            that.skus[i].is_favorable_policy &&
                            that.skus[i].is_favorable_policy === 1
                        ) {
                            if (!that.skus[i].favorable_policy) {
                                that.$message.error(
                                    `[${that.skus[i].title}] 规格未赋码请选择优惠政策类型`,
                                );
                                flg = false;
                                break;
                            }
                        }
                        if (
                            [1, 2, 3, 4].indexOf(that.skus[i].free_of_tax) ===
                            -1
                        ) {
                            that.$message.error(
                                `[${that.skus[i].title}] 规格未赋码请选择免税类型`,
                            );
                            flg = false;
                            break;
                        }
                    }
                }
                if (!flg) {
                    return false;
                }
            }
            return true;
        },

        //验证价格
        verifyPrice(price) {
            if (price == null || price == '') {
                let priceInt = parseInt(price);
                if (priceInt == 0) {
                    return true;
                }
                return false;
            }
            return true;
        },

        //批量处理skus
        skusBatch() {
            let that = this;
            switch (that.batchValueName) {
                case 'batchStock':
                    that.skus.forEach((sku) => {
                        sku.stock = that.batchInputValue;
                    });
                    break;
                case 'batchOriginPrice':
                    that.skus.forEach((sku) => {
                        sku.origin_price = that.batchInputValue;
                    });
                    break;
                case 'batchPrice':
                    that.skus.forEach((sku) => {
                        sku.price = that.batchInputValue;
                    });
                    break;
                case 'batchCostPrice':
                    that.skus.forEach((sku) => {
                        sku.cost_price = that.batchInputValue;
                    });
                    break;
                case 'batchGuidePrice':
                    that.skus.forEach((sku) => {
                        sku.guide_price = that.batchInputValue;
                    });
                    break;
                case 'batchActivityPrice':
                    that.skus.forEach((sku) => {
                        sku.activity_price = that.batchInputValue;
                    });
                    break;
                case 'batchWeight':
                    that.skus.forEach((sku) => {
                        sku.weight = that.batchInputValue;
                    });
                    break;
                case 'batchVolume':
                    that.skus.forEach((sku) => {
                        sku.volume = that.batchInputValue;
                    });
                    break;
                default:
                    break;
            }
            that.cancelBatch();
        },

        save() {},

        // 下一步
        handleNext(data) {
            this.step = 2;
            this.initClassifyTitle(data);
        },
        handleSkusImgSuccess(res, file, index) {
            if (res.code === 0) {
                // this.skus[index].image_url = res.data.file.url
                this.$set(this.skus, index, {
                    ...this.skus[index],
                    image_url: res.data.file.url,
                });
            }
        },
        //恢复数据
        recoverGoods(goods) {
            let that = this;
            // 恢复发票数据
            if (goods.bill_position && goods.bill_position === 1) {
                // 修改部分
                that.tax_code = goods.tax_code;
                that.tax_short_name = goods.tax_short_name;
                // that.tax_option = goods.tax_option
                that.tax_unit = goods.tax_unit;
                that.favorable_policy = goods.favorable_policy;
                that.is_favorable_policy = goods.is_favorable_policy;
                that.free_of_tax = goods.free_of_tax || null;
                that.short_code = goods.short_code;
                that.tax_measure_price = that.$changeMoneyF2Y(
                    goods.tax_measure_price,
                );

                that.taxRateOptios = [
                    // 税率optios
                    { label: '0%', value: 0 },
                    { label: '1%', value: 1 },
                    { label: '3%', value: 3 },
                    { label: '6%', value: 6 },
                    { label: '9%', value: 9 },
                    { label: '10%', value: 10 },
                    { label: '11%', value: 11 },
                    { label: '13%', value: 13 },
                    { label: '17%', value: 17 },
                ];
                if (
                    that.taxRateOptios.indexOfJSON('value', goods.tax_rate) ===
                    -1
                ) {
                    that.taxRateOptios.push({
                        label: `${goods.tax_rate}%`,
                        value: goods.tax_rate,
                    });
                }
                that.tax_rate = goods.tax_rate;
                that.is_tax_logo = goods.is_tax_logo;
                that.tax_product_name = goods.tax_product_name;
            }
            //that.bill_position = goods.bill_position ?? ""; // 新增部分
            if (goods.supplier_id) {
                this.supplier_id = goods.supplier_id;
                this.source_id = goods.supplier_source_id
                    ? goods.supplier_source_id
                    : null;
                this.source_classify_id = goods.supplier_source_category_id
                    ? goods.supplier_source_category_id
                    : null;
                this.getSourceOption();
                this.getSourceClassifyOption();
            }
            that.typeRadio = goods.single_option;
            if (that.typeRadio === 1) {
                that.sku_id = goods.sku_id;
            }
            if (goods.brand_id) {
                that.brand_id = goods.brand_id;
            }
            that.shop_level = goods.shop_level || 0;
            that.des_level = goods.des_level || 0;
            that.express_level = goods.express_level || 0;
            that.level = goods.level || 0;
            //商品id
            that.goodsId = goods.id;
            //恢复分类
            that.getClassify2o3(2, goods.category1_id);
            that.getClassify2o3(3, goods.category2_id);
            setTimeout(() => {
                that.classifyCheck1 = goods.category1_id;
                that.classifyCheck2 = goods.category2_id;
                that.classifyCheck3 = goods.category3_id;
            }, 700);

            //基本信息
            that.minBuyQty = goods.min_buy_qty;
            that.goodsTitle = goods.title;
            that.sn = goods.sn;
            that.barcode = goods.barcode;
            that.code = goods.code;
            that.goodsDesc = goods.desc;
            that.goodsImageUrl = goods.image_url;
            that.videoUrl = goods.video_url;
            that.goodsSort = goods.sort;
            that.goodsGallery = [];
            that.goodsGalleryFileList = [];
            that.status_lock = goods.status_lock ?? 0;
            that.is_display = goods.is_display ?? 0;
            that.is_auto_update_cost = goods.is_auto_update_cost ?? 0;
            that.is_auto_update_price = goods.is_auto_update_price ?? 0;
            that.gather_supply = goods.gather_supply ?? {};
            if (goods.gallery) {
                goods.gallery.forEach((item) => {
                    that.goodsGallery.push({
                        url: item.src,
                        src: item.src,
                        type: item.type,
                    });
                });
            }
            that.goodsGalleryFileList = [...that.goodsGallery];
            that.goodsSales = goods.sales;
            if (goods.gather_supply_id !== 0 && goods.source !== 118 && goods.source !== 136) {
                that.goodsFreightType = '2';
            } else {
                that.goodsFreightType =
                    goods.freight_type || goods.freight_type == 0
                        ? goods.freight_type.toString()
                        : '';
            }
            /* getSupplySetting({ id: goods.gather_supply_id }).then((res) => {
                if (res.code === 0) {
                    this.priceIsEdit = res.data.setting.data.setting.edit_price;
                }
            }); */

            that.goodsFreightPrice = that.$changeMoneyF2Y(goods.freight);
            if (goods.freight_template_id === 0) {
                that.freight_template_id = null;
            } else {
                that.freight_template_id = goods.freight_template_id;
            }
            //商品详情
            that.goodsDetailImages = goods.detail_images;
            /* that.goodsDetailImages = [];
            that.goodsDetailImagesFileList = [];
            goods.detail_images.forEach(item => {
                that.goodsDetailImages.push({
                    url: item
                })
            });
            that.goodsDetailImagesFileList = [...that.goodsDetailImages]; */
            //商品参数
            that.goodsParamList = goods.attrs || [];
            //销售属性
            that.goodsUnit = goods.unit;
            that.goodsPrice = that.$changeMoneyF2Y(goods.price);
            that.goodsOriginPrice = that.$changeMoneyF2Y(goods.origin_price);
            that.goodsCostPrice = that.$changeMoneyF2Y(goods.cost_price);
            that.goodsGuidePrice = that.$changeMoneyF2Y(goods.guide_price);
            that.goodsActivityPrice = that.$changeMoneyF2Y(
                goods.activity_price,
            );
            // 深拷贝skus数组，并确保original_sku_id保持为字符串以避免精度丢失
            that.skus = goods.skus.map(sku => ({
                ...sku,
                original_sku_id: String(sku.original_sku_id)
            }));
            if (that.typeRadio === 1) {
                that.skus.forEach((item) => {
                    item.cost_price = that.$changeMoneyF2Y(item.cost_price);
                    item.activity_price = that.$changeMoneyF2Y(
                        item.activity_price,
                    );
                    item.origin_price = that.$changeMoneyF2Y(item.origin_price);
                    item.price = that.$changeMoneyF2Y(item.price);
                    item.guide_price = that.$changeMoneyF2Y(item.guide_price);
                });
                that.goodsSn = goods.sn;
                that.goodsBarcode = goods.barcode;
                that.goodsCode = goods.code;
                that.goodsWeight = goods.skus[0].weight;
                that.goodsVolume = goods.volume;
                that.goodsStock = goods.stock;
                that.singleSkuImg = goods.skus[0].image_url;
                that.original_sku_id = String(goods.skus[0].original_sku_id);
            } else {
                that.goodsStock = goods.stock;
                that.skus.forEach((item) => {
                    if (item.options) {
                        item.optionName = that.recoverSkuItemOptionName(
                            item.options,
                        );
                    }
                    item.cost_price = that.$changeMoneyF2Y(item.cost_price);
                    item.origin_price = that.$changeMoneyF2Y(item.origin_price);
                    item.price = that.$changeMoneyF2Y(item.price);
                    item.guide_price = that.$changeMoneyF2Y(item.guide_price);
                    item.activity_price = that.$changeMoneyF2Y(
                        item.activity_price,
                    );
                });
                if (that.skus[0].options) {
                    that.recoverSpec(that.skus[0].options);
                }
            }
            that.goodsAffiche = goods.qualifications
                ? [...goods.qualifications]
                : [];
            that.goodsAffiche.forEach((item) => {
                item.url = item.src;
                item.src = item.src;
            });
        },
        //组装spec
        recoverSpec(options) {
            let that = this;
            let specItemsMap = new Map();
            for (let i = 0; i < options.length; i++) {
                that.addSpecItem(options[i].spec_name);
                //创建map值
                specItemsMap.set(options[i].spec_name, []);
                that.skus.forEach((sku) => {
                    let arr = sku.optionName.split('+');
                    let tempMapItems = specItemsMap.get(options[i].spec_name);
                    tempMapItems.push(arr[i]);
                    const set = new Set(tempMapItems);
                    tempMapItems = [...set]; //去重操作
                    specItemsMap.set(options[i].spec_name, tempMapItems);
                });
                that.updateSpecSubdataItemByNames(
                    i,
                    specItemsMap.get(options[i].spec_name),
                );
            }
        },

        //组装optionName
        recoverSkuItemOptionName(options) {
            let optionName = '';
            for (let i = 0; i < options.length; i++) {
                if (i === options.length - 1) {
                    optionName =
                        optionName +
                        options[i].spec_item_name.replace(/\+/g, '/pj/^');
                } else {
                    optionName =
                        optionName +
                        options[i].spec_item_name.replace(/\+/g, '/pj/^') +
                        '+';
                }
            }
            return optionName;
        },
    },
};
