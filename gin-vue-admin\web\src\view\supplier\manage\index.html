<m-card>
    <el-button @click="newSupplier()" type="primary" >新增</el-button>
    <div class="search-term mt25">
        <el-form :model="formData" ref="formData" class="demo-form-inline" label-width="90px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.keyword" clearable class="line-input-width">
                    <el-select v-model="formData.type" slot="prepend" clearable filterable>
                        <el-option value="1" label="会员id"></el-option>
                        <el-option value="2" label="申请人姓名"></el-option>
                        <el-option value="3" label="手机号"></el-option>
                    </el-select>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.name" class="line-input" clearable>
                    <span slot="prepend">供应商名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >供应商分类</span>
                    </div>
                    <el-select v-model="formData.category_id" value-key="value" filterable placeholder="请选择" label="分类"
                                :style="{ width: '100%' }">
                        <el-option v-for="item in supplierCategoryList" :label="item.name" :value="item.id">
                            {{ item.name }}
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <br>
            <el-form-item>
                <el-button @click="onSubmit" type="primary">查询</el-button>
                <el-button type="text" @click="resetSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
    </div>
    <el-table class="mt25" :data="tableData">
        <el-table-column label="ID" prop="id" align="center"></el-table-column>
        <el-table-column label="供应商名称" prop="name" align="center"></el-table-column>

        <el-table-column label="登录账户" prop="user_info.userName" align="center"></el-table-column>

        <el-table-column label="姓名" prop="realname" align="center"></el-table-column>

        <el-table-column label="手机号" prop="mobile" align="center"></el-table-column>

        <el-table-column label="分类名称" prop="category_info" align="center">
            <template slot-scope="scope">
                {{ scope.row.category_info.name }}
            </template>
        </el-table-column>

        <el-table-column label="商品数量" prop="goods_count" align="center">
            <template slot-scope="scope">
                {{ scope.row.all_goods_count }}
            </template>
        </el-table-column>

        <el-table-column label="订单总额" prop="order_price_total" align="center">
            <template slot-scope="scope">
                {{ scope.row.order_price_total | formatF2Y }}
            </template>
        </el-table-column>
        <el-table-column label="结算余额" prop="order_price_total" align="center">
            <template slot-scope="scope">
                {{ scope.row.settlement_balance | formatF2Y }}
            </template>
        </el-table-column>
        <el-table-column label="结算方式" align="center">
            <template slot-scope="scope">
                <span v-if="scope.row.deduction_type === 1">独立设置</span>
                <span v-if="scope.row.deduction_type === 2 || scope.row.deduction_type === 0">平台设置</span>
            </template>
        </el-table-column>
        <el-table-column label="比例(%)" align="center">
            <template slot-scope="scope">
                {{ scope.row.deduction_ratio / 100 }}
            </template>
        </el-table-column>
        <el-table-column label="日期" align="center">
            <template slot-scope="scope">{{
                    scope.row.created_at | formatDate
                }}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="230px" align="center" fixed="right">
            <template slot-scope="scope">
                <el-button type="text" @click="copyLink(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">复制链接</el-button>
                <el-button class="table-button" @click="editSupplier(scope.row)" type="text" style="padding: 0 !important;margin-left: 10px !important;">编辑
                </el-button>
                <el-button class="table-button" @click="changePassword(scope.row.id)" type="text" style="padding: 0 !important;margin-left: 10px !important;">修改密码
                </el-button>
<!--                <el-button @click="deleteSupplierDialog(scope.row)" type="text" class="color-red">{{
                        scope.row.status ==
                        0 ? "冻结" : "解冻"
                    }}
                </el-button>-->
                <el-button @click="deleteSupplierDialog(scope.row)" type="text" class="color-red" style="padding: 0 !important;margin-left: 10px !important;">{{
                        scope.row.status ==
                        0 ? "禁用" : "启用"
                    }}
                </el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                   :style="{ float: 'right', padding: '20px' }" :total="total" @current-change="handleCurrentChange"
                   @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper"></el-pagination>

    <el-dialog :before-close="closeDialog" :visible="dialogFormVisible" :title="dialogTitle">
        <el-form :model="supplierData" label-position="right" label-width="15%" :rules="rule" ref="el-forms">
            <el-form-item label="供应商名称:" prop="name">
                <el-input v-model="supplierData.name" clearable placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item label="会员:" prop="uid">
                <!-- <el-input v-model="supplierData.uid" clearable placeholder="请输入"></el-input> -->
                <el-select
                           filterable
                           remote
                           reserve-keyword
                           v-model="supplierData.uid"
                           clearable
                           :remote-method="remoteMethod"
                           class="w100">
                    <el-option v-for="item in userOption" :key="item.id" :label="item.username"
                               :value="item.id"></el-option>
                    <div class="text-center">
                        <el-pagination background small class="pagination"
                                       style="padding-top:10px !important;padding-bottom: 0 !important;"
                                       :current-page="userOptionsPage.page"
                                       :page-size="userOptionsPage.pageSize"
                                       :total="userOptionsPage.total"
                                       @current-change="handleUserPage"
                                       layout="prev,pager, next"/>
                    </div>
                </el-select>
            </el-form-item>

            <el-form-item label="姓名:" prop="realname">
                <el-input v-model="supplierData.realname" clearable placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item label="手机号:" prop="mobile">
                <el-input v-model="supplierData.mobile" clearable placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item v-if="!supplierData.id" label="账号:" prop="user_info.username">
                <el-input v-model="supplierData.user_info.username" clearable placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item v-if="!supplierData.id" label="密码:" prop="user_info.password">
                <el-input type="password" v-model="supplierData.user_info.password" clearable
                          placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item label="分类:" prop="category_id">
                <el-select v-model="supplierData.category_id" value-key="value" placeholder="请选择" label="分类"
                           :style="{ width: '100%' }">
                    <el-option v-for="item in supplierCategoryList" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="寄件人手机号:" prop="express_mobile">
                <el-input v-model="supplierData.express_mobile" clearable
                placeholder="请输入寄件人手机号"></el-input>
                <p class="color-grap">物流接口查询快递信息时需要提供寄件人手机号，不填写会导致物流查询功能异常</p>
            </el-form-item>

            <el-form-item label="商品审核:" prop="need_verify">
                <el-radio-group v-model="supplierData.need_verify">
                    <el-radio :label="1">需要</el-radio>
                    <el-radio :label="2">不需要</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="创建品牌权限:" prop="need_verify">
                <el-radio-group v-model="supplierData.brand_auth">
                    <el-radio :label="1">允许创建</el-radio>
                    <el-radio :label="2">不允许创建</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="选品列表:" prop="need_verify">
                <el-radio-group v-model="supplierData.is_storage">
                    <el-radio :label="0">显示</el-radio>
                    <el-radio :label="1">不显示</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="平台扣点:">
                <el-radio-group v-model="supplierData.deduction_type">
                    <el-radio :label="1">独立设置</el-radio>
                    <el-radio :label="2">平台设置</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="比例:" v-if="supplierData.deduction_type === 1">
                <div class="f fac">
                    <el-input-number v-model="supplierData.deduction_ratio" :min="0" :precision="2" :controls="false"
                                     class="f1 number-text-left">
                    </el-input-number>
                    <p class="ml10">%</p>
                </div>
            </el-form-item>

            <el-form-item label="结算金额:" v-if="supplierData.deduction_type === 1">
                <div class="f fac">
                    <el-form-item>
                        <el-radio :label="0" v-model="supplierData.sellt_type">订单金额(默认)</el-radio>
                    </el-form-item>
                    <el-form-item style="margin-left: 50px" prop="sellt_type">
                        <el-radio :label="1" v-model="supplierData.sellt_type">供货价+运费</el-radio>
                    </el-form-item>
                </div>
            </el-form-item>
            <el-form-item label="租赁:">
                <el-radio-group v-model="supplierData.is_lease">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="2">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="后台登录页LOGO:">
                <el-upload class="avatar-uploader" :show-file-list="false"
                           :action="path + '/fileUploadAndDownload/upload'"
                           :headers="{ 'x-token': token }" :on-success="handleAdminLoginLogoImgSuccess"
                           :before-upload="$fn.beforeAvatarUpload"
                           accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                    <m-image v-if="supplierData.admin_login_logo" style="width:100%;height:100%"
                             :src="supplierData.admin_login_logo">
                    </m-image>
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                <p class="color-grap">建议尺寸：100*80像素,图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
            </el-form-item>
            <el-form-item label="商城名称:">
                <el-input v-model="supplierData.admin_name" clearable
                placeholder="请输入商城名称"></el-input>
            </el-form-item>
            <el-form-item label="后台登录页标题:">
                <el-input v-model="supplierData.admin_title" clearable
                placeholder="请输入后台登录页标题"></el-input>
            </el-form-item>
            <el-form-item label="底部信息:">
                <m-editor v-model="supplierData.admin_content"
                          style="margin-bottom: 43px;"></m-editor>
            </el-form-item>
        </el-form>
        <div class="dialog-footer" slot="footer">
            <el-button @click="submitVerify()" type="primary">确 定</el-button>
            <el-button @click="closeDialog">取 消</el-button>
        </div>
    </el-dialog>
    <!--复制链接弹窗-->
    <el-dialog :visible.sync="copyVisible" title="复制链接" width="30%">
        <div class="f fac copyBox" >
            <el-button @click="pclinkCopy">pc端链接</el-button>
            <el-button  @click="H5linkCopy">H5链接</el-button>
            <el-button @click="appletlinkCopy">小程序链接</el-button>
            <el-button @click="adminlinkCopy">后台链接</el-button>
        </div>
        <span class="txinfo">点击按钮复制对应的链接！</span>
        <div slot="footer" class="dialog-footer" >
            <el-button @click="copyCloseBrand" type="primary">关 闭</el-button>
        </div>
    </el-dialog>
    <el-dialog :before-close="closePasswordDialog" :visible="dialogPasswordFormVisible" :title="dialogPasswordTitle">
        <el-form :model="change" label-position="right" label-width="15%" :rules="rulePassword" ref="el-password">
            <el-form-item label="修改密码:" prop="new_password">
                <el-input v-model="change.new_password" clearable placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="确认修改密码:" prop="password_again">
                <el-input v-model="change.password_again" clearable placeholder="请输入"></el-input>
            </el-form-item>
        </el-form>
        <div class="dialog-footer" slot="footer">
            <el-button @click="submitPassword()" type="primary">确 定</el-button>
            <el-button @click="closePasswordDialog">取 消</el-button>
        </div>
    </el-dialog>
</m-card>