import {getDict} from "@/utils/dictionary";

export default {
    data() {
        return {
            page: 1,
            total: 10,
            pageSize: 10,
            tableData: [],
            searchInfo: {
                dateType: 0, // 日期类型 0开始时间 1结束时间
                key_word:"",
            },
            dataInfo: {},
            payTypeList: [],
            resData: {}, // 整个返回data 方便有统计字段单独处理
            accountBalancesTotals:[], // 会员余额列表字段
            type_list:[],

            dateTypeOptios: [
                {label: "订单最早下单时间", value: 0},
                {label: "订单最晚下单时间", value: 1},
                {label: "导出时间", value: 2},
            ],
            brand_auth: 0
        }
    },
    methods: {
        filterDict(value, type) {
            const rowLabel = this[type + "Options"] && this[type + "Options"].filter(item => item.value == value)
            return rowLabel && rowLabel[0] && rowLabel[0].label
        },
        async getDict(type) {
            const dicts = await getDict(type)
            this[type + "Options"] = dicts
            return dicts
        },
        handleSizeChange(val) {
            this.pageSize = val
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.page = val
            this.getTableData()
        },
        async getTableData(page = this.page, pageSize = this.pageSize) {
            for (let k in this.searchInfo){
                if(typeof this.searchInfo[k] !== "number" && !this.searchInfo[k]){
                    delete this.searchInfo[k]
                }
            }
            const table = await this.listApi({page, pageSize, ...this.searchInfo})
            if (table.code == 0) {
                //处理数字字段 分与元之间的转换关系
                if (this.handleNumberColumn && typeof (this.handleNumberColumn) == "function") {
                    this.tableData = this.handleNumberColumn(table.data.list);
                } else {
                    this.tableData = 'list' in table.data ? table.data.list : table.data;
                }
                if(table.data.type_list){
                    this.type_list = table.data.type_list
                }
                this.accountBalancesTotals = table.data.accountBalancesTotals || []
                this.resData = table.data
                this.select_options = table.data.pay_type;
                this.dataInfo = table.data.data;

                this.brand_auth = table.data.brand_auth || 0

                if (table.data.pay_type) {
                    this.payTypeList = table.data.pay_type
                }
                this.total = table.data.total
                // this.page = table.data.page
                this.pageSize = table.data.pageSize
            }
        },

        // 导出
        async exportTable() {
            for (let k in this.searchInfo){
                if(typeof this.searchInfo[k] !== "number" && !this.searchInfo[k]){
                    delete this.searchInfo[k]
                }
            }
            const res = await this.exApi({...this.searchInfo})
            if (res.code === 0) {
                window.location.href = this.$path + '/' + res.data.link
            } else {
                this.$message.error(res.msg)
            }
        }
    }
}